#!/bin/bash
echo "Starting ProFTPD..."
SSL_DIR="/etc/proftpd/ssl"
if [ ! -f "$SSL_DIR/proftpd.key" ] || [ ! -f "$SSL_DIR/proftpd.crt" ]; then
    echo "SSL certificates not found, generating new ones..."
    openssl req -x509 -nodes -days 3650 -newkey rsa:2048 \
    -keyout $SSL_DIR/proftpd.key \
    -out $SSL_DIR/proftpd.crt \
    -config $SSL_DIR/openssl.cnf
else
    echo "Using existing SSL certificates."
fi
# /usr/sbin/proftpd -nd10
/usr/sbin/proftpd -nd5
