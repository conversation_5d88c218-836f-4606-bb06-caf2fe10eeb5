<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE hibernate-configuration
    PUBLIC "-//Hibernate/Hibernate Configuration DTD//EN"
    "http://hibernate.sourceforge.net/hibernate-configuration-3.0.dtd">
  <!--
    Copyright (c) 2008. All rights reserved. - - PCI Geomatics, 90 Allstate 
    Parkway, Markham, Ontario, Canada. - - Not to be used,
    reproduced or disclosed without permission. -
  -->
<hibernate-configuration>
  <session-factory>

    <!-- Hibernate configuration parameters -->
    <!-- local connection properties -->
    <property name="hibernate.connection.url">
      jdbs:postgresql://localhost:5432/jps
    </property>
    
    <property name="hibernate.connection.driver_class">
      org.postgresql.Driver
    </property>

    <property name="hibernate.connection.username">jps</property>
    <property name="hibernate.connection.password">jps</property>
    
<!--    
    <property name="hibernate.connection.username">postgres</property>
    <property name="hibernate.connection.password">postgres</property>
-->
    <property name="hibernate.connection.pool_size">5</property>
    <property name="hibernate.c3p0.min_size">5</property>
    <property name="hibernate.c3p0.max_size">20</property>
    <property name="hibernate.c3p0.timeout">1800</property>
    <!-- 
    <property name="hibernate.c3p0.max_statements">50</property>
    -->

    <!-- Enable Hibernate's automatic session context management -->
    <property name="current_session_context_class">thread</property>

    <!-- dialect for PostgreSQL -->
    <property name="dialect">org.hibernate.dialect.PostgreSQLDialect</property>
    <property name="hibernate.show_sql">false</property>
    <property name="hibernate.transaction.factory_class">
      org.hibernate.transaction.JDBCTransactionFactory
    </property>

    <mapping resource="job.hbm.xml" />
    <mapping resource="scene_catalog.hbm.xml" />
    <mapping resource="job_state.hbm.xml" />
        
  </session-factory>
</hibernate-configuration>