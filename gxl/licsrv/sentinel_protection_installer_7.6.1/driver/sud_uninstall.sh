#!/bin/sh
 ###########################################################################
 #            Copyright (C) 2015 SafeNet, Inc.                             #
 #                   All Rights Reserved                                   #
 #                                                                         #
 # sud_uninstall.sh : This uninstalls sentinel usb daemon version 7.5.4    #
 #                                                                         #
 ###########################################################################
SUD_INSTALL_DIR=/opt/safenet_sentinel/common_files
sntl_product_info=/var/opt/sentinel_keys/shk_product.info
old_sntl_product_info=/var/opt/sentinel/sntl_product.info
MAJOR=1
MINOR=3
REV=1
version=1.3.1
SUD_VER=754
sud_version=7.5.4
SUD_RPM=sntl-sud

#
# This script uninstall usb daemon.
#

###############################################################################
#                      Check for Dependencies
###############################################################################
check_dep()
{
	#check for super user
	user_id=`id -u`
	if [ $user_id -ne 0 ]
	then
		echo "WARNING:To Install this software make sure you have Super User (root) permission."  
		echo "Installation Aborted."
		exit
	fi
}

showbanner()
{ 
	echo "-------------------------------------------------------------------------------"
	echo "              Sentinel Keys USB Daemon $sud_version Un-Installation Script"
	echo "                         Copyright (C) 2015 SafeNet, Inc."              
	echo "                             All rights reserved."
	echo "-------------------------------------------------------------------------------"
}



################################################################################
##                 Start Here
################################################################################
  
  check_dep
  if [ "$1" != "nobanner" ] ; then
	showbanner
  fi


    rpm -q $SUD_RPM-$sud_version > /dev/null 2>&1
    if [ $? -eq 0 ]
    then
		#rpm -e $SUD_RPM-$sud_version > /dev/null 2>&1
		rpm -e $SUD_RPM-$sud_version 2>/dev/null
		if [ $? -ne 0 ]
		then 
			 #rpm -e sntl-sud-7.5.0 2>&1 | grep "error: Failed dependencies:" 1>/dev/null 2>&1
		     #if [ $? -ne 0 ]
			 #then
				if [ -f "$sntl_product_info" ]
				then
    				tmp_var=`grep -c sud $sntl_product_info|cut -d : -f 4`
    				if [ -n "$tmp_var" ]
    				then
        			if [ $tmp_var -eq 1 ]
        			then
                	current_inst=`grep ins-sud $sntl_product_info|cut -d = -f 3`
					tmp_var=`grep -c SSPSUPSupport $sntl_product_info`

    				if [ -n "$tmp_var" ] && [ "$tmp_var" -eq 1 ] && [ "$current_inst" -gt 2 ]
    				then

				 	count=`expr $current_inst - 1`
					sed -i "/^sud=sntl-sud/d" $sntl_product_info > /dev/null 2>&1
echo "sud=sntl-sud-$sud_version:$SUD_VER:$SUD_INSTALL_DIR:ins-sud=$count" >> $sntl_product_info
				fi
				fi
				fi
    		#fi
		fi

			
	    		echo "An error occured while uninstalling the SUD."
		else 
			#echo "Sentinel Keys USB Daemon uninstalled successfully"
			if [ -f "$sntl_product_info" ]
		        then
                		info_data=`grep product $sntl_product_info`
		                info_data=${info_data}`grep sud $sntl_product_info`
                		info_data=${info_data}`grep server $sntl_product_info`

		                if [ -z "$info_data" ]
                		then
		                        rm -rf `dirname $sntl_product_info`
                		fi
        		fi


		fi
    else
		echo "Sentinel Keys USB Daemon $sud_version package not installed."
    fi
  echo "-------------------------------------------------------------------------------"
  
  
