#!/bin/sh
 ###########################################################################
 #            Copyright (C) 2015 SafeNet, Inc.                             #
 #                   All Rights Reserved                                   #
 #                                                                         #
 # sud_install.sh : This install sentinel usb daemon version 7.5.4         #
 #                                                                         #
 ###########################################################################
SUD_INSTALL_DIR=/opt/safenet_sentinel/common_files
sntl_product_info=/var/opt/sentinel_keys/shk_product.info
old_sntl_product_info=/var/opt/sentinel/sntl_product.info
MAJOR=1
MINOR=3
REV=1
version=1.3.1
SUD_VER=754
sud_version=7.5.4
SUD_RPM=sntl-sud

#
# This script install usb daemon.
#

check_dep()
{
  #check for super user
        user_id=`id -u`
        if [ $user_id -ne 0 ]
        then
                echo "ERROR:To Install this software make sure you have Super User (root) permission."
                echo "Installation Aborted."
                exit
        fi

}
###############################################################################
                        # Show SHK  Banner
###############################################################################
showbanner()
{
echo "-------------------------------------------------------------------------------"
echo "                Sentinel Keys USB Daemon $sud_version Installation Script"
echo "                         Copyright (C) 2015 SafeNet, Inc."
echo "                             All rights reserved."
echo "-------------------------------------------------------------------------------"
}

uninstall_sud()
{
SHK_DEMON_RET=0
OLD_DEMON_RET=0
SHK_DEMON_VER=0
[ -f "$sntl_product_info" ]
        if [ $? -eq 0 ]
        then
                SHK_DEMON_VER=`grep sud $sntl_product_info|cut -d = -f 2|cut -d : -f 2`
        if [ -z $SHK_DEMON_VER ]
        then
                        SHK_DEMON_RET=0 #No SHK SUD 7.4.0 exist
        else
                        if [ $SHK_DEMON_VER -le $SUD_VER ]
                        then
                                SHK_DEMON_RET=1  # SUD 7.4.0 DEMON version exist
                        #elif [ $SHK_DEMON_VER -gt $SUD_VER ]
			#then
			else

                                #Higher DEMON version already installed. Do Nothing & exit
                                SHK_DEMON_RET=3 #> SUD 7.4.0 DEMON version exist
                                echo -e "\n ERROR:Installer has detected a higher version of Sentinel USB Daemon on your system"
                                exit 1
                        fi
        fi
        fi

}

showlicense()
{
if [ $LicenseSHOW == 0 ]    
then
    #LicAgrmntFound indicates whether EULA.txt file present or not
    # value 0 (zero) indicates, its not present, 1 (one) indicates its present.
    #Initially we assume EULA.txt is not present.
    LicAgrmntFound=0

    echo $line
    [ -f $LicensePATH1/EULA.txt ] 
    if [ $? == 0 ] 
    then
        LicAgrmntFound=1
        LicensePATH=$LicensePATH1
    fi

    [ -f $LicensePATH2/EULA.txt ]
    if [ $? == 0 ]
    then
        LicAgrmntFound=1
        LicensePATH=$LicensePATH2
    fi

    if [ $LicAgrmntFound == 1 ]
    then
	more $LicensePATH/EULA.txt
	while [ 1 ]
	do 
	  echo -n "Do you Agree with the License(y/n): "
	  read choice
	  case $choice in 
	    'y') 
		break 2
		;;
	    'Y') 
		break 2
		;;
	    'n') 
		exit
		;;
	    'N') 
		exit
		;;
	    *)  
	    	echo "-------------------------------------------------------------------------------"
		;;
	  esac	  
	done
    else
	echo "\"EULA.txt\" file missing."
	echo $line
	exit
    fi
fi
}


#############################################################################
# 			Start Here
##############################################################################

LicensePATH1=.
LicensePATH2=..
LicenseSHOW=${#LicenseSHOW}
showlicense
if [ "$1" != "nobanner" ] ; then
	showbanner
fi
check_dep
uninstall_sud
if [ $SHK_DEMON_RET -eq 1 ] && [ $SHK_DEMON_VER -ne 731 ] && [ $SHK_DEMON_VER -ne 730 ] && [ $SHK_DEMON_VER -ne 740 ]
then
	echo "Installing Sentinel USB Daemon $sud_version"
        rpm -ivh --force $SUD_RPM-$sud_version-0.i386.rpm
        if [ $? -ne 0 ]; then
                echo "Installation Failed."
                exit
        fi

elif [ $SHK_DEMON_RET -eq 3 ] # Higher SUD version exists
then
	echo "Installer has detected a higher version of Sentinel USB Daemon on your system" 
	echo "Skipping Sentinel USB Daemon $sud_version Installation..."
	exit
else        # In any other case install the USB Daemon 

######################################################################
#		Daemon Upgrade message
######################################################################
num_product=0
	if [ -f "$old_sntl_product_info" ]
        then
        	num_product=`grep product $old_sntl_product_info -c`
                if [ $num_product -eq 0 ]
                then
                	num_product=`grep sud $old_sntl_product_info -c`
                fi
        fi
	if [ -z "$SHK_DEMON_VER" ]
	then
		SHK_DEMON_VER=0
	fi
	if [ $num_product -gt 0 ] || [ $SHK_DEMON_VER -eq 731 ] || [ $SHK_DEMON_VER -eq 730 ] || [ $SHK_DEMON_VER -eq 740 ] || [ $SHK_DEMON_VER -eq 750 ]
        then
	if [ $LicenseSHOW == 0 ]
	then		
		echo $line
                echo -e "Type \"y\" to overwrite the older version  of the Sentinel System Driver (daemon) found  on  this  system. Do not  cancel the upgrade before the installation has completed  or  you  may  not  be  able  to  run applications that depend on the software components being installed."
                # echo -n "       that depend on the software components being installed"
                echo -e "\nType \"n\" to  cancel  this  installation  without  upgrading the Sentinel System Driver (daemon) existing on this system."
                echo $line
        	IsContinue="a"
	        while [ "$IsContinue" != "n" -o "$IsContinue" != "y" ]
        	do
                	echo -n "Enter your choice (y/n): "
	                read -r IsContinue
        	        if [ "$IsContinue" == "n" ]
                	then
                        	echo "Installer Aborted by User"
	                        exit
        	        elif [ "$IsContinue" == "y" ]
                	then
                        	break
                	else
                        	IsContinue="a"
                	fi
        	done
	fi
	fi
############################################################################################
	 if [ $SHK_DEMON_VER -eq 740 ] || [ $SHK_DEMON_VER -eq 750 ]
         then

                flag=0
                if [ -f "$sntl_product_info" ]
                then
                        tmp_var=`grep -c sud $sntl_product_info|cut -d : -f 4`
                        if [ -n "$tmp_var" ]
                        then
                                if [ $tmp_var -eq 1 ]
                                then
                                        current_inst=`grep ins-sud $sntl_product_info|cut -d = -f 3`
                                fi
                        fi
                fi
                sed -i "/^sud=sntl-sud/d" $sntl_product_info > /dev/null 2>&1
                echo "sud=sntl-sud-7.5.4:754:$SUD_INSTALL_DIR:ins-sud=1" >> $sntl_product_info
                rpm -e $SUD_RPM-7.5.4 --nodeps >/dev/null 2>&1
                sed -i "/^sud=sntl-sud/d" $sntl_product_info > /dev/null 2>&1
                echo "sud=sntl-sud-7.5.4:754:$SUD_INSTALL_DIR:ins-sud=$current_inst" >> $sntl_product_info

	fi

##############################################################################################	
	echo "Installing Sentinel USB Daemon $sud_version"
	rpm -ivh --force $SUD_RPM-$sud_version-0.i386.rpm	
	if [ $? -ne 0 ]; then 
		echo "Installation Failed."
		exit
	fi	
fi

echo "-------------------------------------------------------------------------------"


	
