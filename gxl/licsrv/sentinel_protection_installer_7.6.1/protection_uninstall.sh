#!/bin/sh
PACKAGE_DIR=/opt/sentinel
PRODUCT_NAME=Superpro
MAJOR=6
MINOR=6
version=6.6.1
spi_version=7.6.1
sud_version=7.5.4
srv_version=7.6.1
SUD_RPM=sntl-sud
SERVER_RPM=sntl-server
SPI_RPM=spro-spi
SPI_INSTALL_DIR=/opt/sentinel/Superpro/6.6/sentinel_protection_installer


#
# check for dependency
#
check_dep()
{
   #check for root
   user_id=`id -u`
   if [ $user_id -ne 0 ]
   then
      echo "WARNING: To install this software make sure you have Super User (root) permission."  
      echo "Installation aborted."
      exit
   fi

}

#
# check OS
#
check_OS()
{

   found=0
   for OSL in SUSE Debian Ubuntu Fedora
   do
      if [ $found -eq 0 ]
      then
         cat /etc/issue |grep $OSL >/dev/null 2>&1
         if [ $? -eq 0 ]
         then
            OS=$OSL
            found=1
         fi
      fi
   done

   if [ "$OS" = "Ubuntu" ]
   then
      OS="Debian"
   fi

   if [ -n "$OS" ]
   then
      # not support Debian/Ubuntu
      if [ "$OS" = "Debian" ]
      then
         echo "WARNING: Installation program does not support this system."
         echo "Installation aborted."
         echo $line
         exit
      fi
   fi

}

###############################################################################
ask()
{
   echo $line
   choice=-1
   while [ "$choice" != "1" ] && [ "$choice" != "0" ]
   do
      echo "   1 : Un-install Sentinel System Driver and Sentinel Protection Server "
      echo "   0 : Exit                                 "
      echo -n "Please enter your choice: "
      read choice
   done
   echo $line

}

###############################################################################
errchecking()
{
   if [ $? = 0 ]
   then
      echo "Successfully un-installed $Package "
      echo $line
   else
      echo "$Package un-installation failed"
      echo "WARNING:To uninstall this software make sure that you have Super user (root) permissions."
      echo $line
   fi

}

###############################################################################
clear
line="-------------------------------------------------------------------------------"
choice=-1
ret=0

check_dep
check_OS

echo $line
echo "              Sentinel Protection Installer $spi_version Un-Installation Script "
echo "                            Copyright (C) 2009 SafeNet, Inc."
echo "                                All rights reserved."
echo $line
echo "This script will un-install "
echo "    - Sentinel System Driver ($sud_version) "
echo "    - Sentinel Protection Server ($srv_version)"

ask
if [ "$choice" == "0" ]
then
   exit
fi

if [ "$choice" == "1" ]
then 
   #Uninstall The Server.
   rpm -q $SERVER_RPM-$srv_version > /dev/null 2>&1
   if [ $? -eq 0 ]
   then
      rpm -e $SERVER_RPM-$srv_version 2>/dev/null
      if [ $? -eq 0 ]
      then
         #Check if other version also exists.
         IsSSP630=0
         IsSSP631=0
      
         rpm -q ssp-6.3.0-0 > /dev/null 2>&1
         if [ $? -eq 0 ]
         then
            # ssp630 found
            IsSSP630=1
         fi
         
         rpm -q ssp-6.3.1-0 > /dev/null 2>&1
         if [ $? -eq 0 ]
         then
            # ssp631 found
            IsSSP631=1
         fi
      
         if [ $IsSSP630 -eq 1 ] || [ $IsSSP631 -eq 1 ]
         then
            rm -rf /opt/RainbowTechnologies/SuperPro/6.3/Server
            rm -rf /opt/RainbowTechnologies/SuperPro/6.3/MonitoringTool
         fi
         
         if [ -f /var/opt/sentinel/sntl_product.info ]
         then
            tmp=`cat /var/opt/sentinel/sntl_product.info`
            if [ -z "$tmp" ]
            then
               rm -rf /var/opt/sentinel/sntl_product.info
               tmp=`ls /var/opt/sentinel`
               if [ -z "$tmp" ]
               then
                  rm -rf /var/opt/sentinel
               fi
            fi
         fi
      else
         echo "An error occurred while uninstalling Sentinel Protection Server."
      fi
      echo $line
   else
      echo "Sentinel Protection Server $srv_version not installed."
      echo $line
   fi
   
   echo "Uninstalling Sentinel System Driver..."
   rpm -q $SUD_RPM-$sud_version > /dev/null 2>&1
   if [ $? -ne 0 ]
   then         
      echo "Sentinel System Driver $sud_version not installed. "
      echo $line
      exit
   fi
   
   rpm -e $SUD_RPM-$sud_version 2>/dev/null
   if [ $? -eq 0 ]
   then
      rpm -q $SUD_RPM-$sud_version > /dev/null 2>&1
      if [ $? -ne 0 ]
      then
         rpm -q $SPI_RPM-$version > /dev/null 2>&1
         if [ $? -ne 0 ]
         then
            # Remove if spi rpm is not installed.
            rm -rf   $SPI_INSTALL_DIR  > /dev/null 2>&1
         else
            (rpm -e $SPI_RPM-$version) > /dev/null 2>&1
         fi
         
         # Remove Web Help and Readme.pdf
         rm -f $PACKAGE_DIR/$PRODUCT_NAME/$MAJOR.$MINOR/protection_uninstall.sh > /dev/null 2>&1
         
         #Remove directories if empty.
         val=`ls $PACKAGE_DIR/$PRODUCT_NAME/$MAJOR.$MINOR/ 2>/dev/null`
         if [ "$val" = "" ]
         then
            rm -rf $PACKAGE_DIR/$PRODUCT_NAME/$MAJOR.$MINOR
         fi
         val=`ls $PACKAGE_DIR/$PRODUCT_NAME/ 2>/dev/null`
         if [ "$val" = "" ]
         then
            rm -rf $PACKAGE_DIR/$PRODUCT_NAME/
         fi
         val=`ls $PACKAGE_DIR/ 2>/dev/null`
         if [ "$val" = "" ]
         then
            rm -rf $PACKAGE_DIR/
         fi
         
         if [ -f /var/opt/sentinel_keys/shk_product.info ]
         then
            tmp=`cat /var/opt/sentinel_keys/shk_product.info`
            if [ -z "$tmp" ]
            then
               rm -rf /var/opt/sentinel_keys/shk_product.info
               tmp=`ls /var/opt/sentinel_keys`
               if [ -z "$tmp" ]
               then
                  rm -rf /var/opt/sentinel_keys
               fi
            fi
         fi
      fi
   else
      echo "An error occurred while uninstalling Sentinel System Driver."
   fi
fi
#############################################################################
