#!/bin/sh
PACKAGE_DIR=/opt/sentinel
PRODUCT_NAME=Superpro
SPI_INSTALL_DIR=/opt/sentinel/Superpro/6.6/sentinel_protection_installer
sntl_product_info=/var/opt/sentinel/sntl_product.info
shk_product_info=/var/opt/sentinel_keys/shk_product.info
SERVER_RPM=sntl-server
SPI_RPM=spro-spi
MAJOR=6
MINOR=6
REV=1
SPI_MAJOR=7
SPI_MINOR=6
SPI_REV=1
version=6.6.1
SUD_VER=754
sud_version=7.5.4
SRV_VER=761
srv_version=7.6.1
spi_version=7.6.1
RELEASE_DATE=0120


#
# check for dependency
#
check_dep()
{
   #check for root
   user_id=`id -u`
   if [ $user_id -ne 0 ]
   then
      echo "WARNING: To install this software make sure you have Super User (root) permission."  
      echo "Installation aborted."
      exit
   fi

}

#
# check OS
#
check_OS()
{

   found=0
   for OSL in SUSE Debian Ubuntu Fedora
   do
      if [ $found -eq 0 ]
      then
         cat /etc/issue |grep $OSL >/dev/null 2>&1
         if [ $? -eq 0 ]
         then
            OS=$OSL
            found=1
         fi
      fi
   done

   if [ "$OS" = "Ubuntu" ]
   then
      OS="Debian"
   fi

   if [ -n "$OS" ]
   then
      # not support Debian/Ubuntu
      if [ "$OS" = "Debian" ]
      then
         echo "WARNING: Installation program does not support this system."
         echo "Installation aborted."
         echo $line
         exit
      fi
   fi

}

###############################################################################
# Show SSP Banner
###############################################################################
showbanner()
{
   echo $line
   echo "                Sentinel Protection Installer $spi_version Installation Script"
   echo "                           Copyright (C) 2009 SafeNet, Inc."
   echo "                               All rights reserved."
   echo $line
   echo "This script will install"
   echo "    - Sentinel System Driver $sud_version"
   echo "    - Sentinel Protection Server $srv_version"

}

###############################################################################
# Show EULA
###############################################################################
showlicense()
{
   if [ $LicenseSHOW == 0 ]
   then
      echo $line
      if [ -f $LicensePATH/EULA.txt ] 
      then
         more $LicensePATH/EULA.txt
         while [ 1 ]
         do 
            echo -n "Do you Agree with the License(y/n): "
            read choice
            case $choice in 
            'y')
               break 2
               ;;
            'Y') 
               break 2
               ;;
            'n') 
               exit
               ;;
            'N') 
               exit
               ;;
            *)
               echo "-------------------------------------------------------------------------------"
               ;;
            esac
         done
      else
         echo "\"EULA.txt\" file missing."
         echo $line
         exit
      fi
   fi

}



###############################################################################
# Show Installation Menu
###############################################################################
showmenu()
{
   echo $line
   choice=-1
   while [ "$choice" != "1" ] && [ "$choice" != "0" ]
   do
      echo "   1 : Install Sentinel System Driver and Sentinel Protection Server "
      echo "   0 : Exit "    
      echo -n "Please enter your choice: "
      read choice
   done
   echo $line

}

check_server()
{
   OLD_SERVER_RET=0
   OLD_SERVER_VER=0
   OLD_SERVER_RPM=0
   
   if [ -f $sntl_product_info ]
   then
      OLD_SERVER_VER=`grep server $sntl_product_info|cut -d = -f 2|cut -d : -f 2`
      OLD_SERVER_RPM=`grep server $sntl_product_info|cut -d = -f 2|cut -d : -f 1`
      if [ -n "$OLD_SERVER_VER" ]
      then
         if [ $OLD_SERVER_VER -eq $SRV_VER ]
         then
            OLD_SERVER_RET=1
            return
         elif [ $OLD_SERVER_VER -lt $SRV_VER ]
         then
            OLD_SERVER_RET=2
            return
         else
            OLD_SERVER_RET=3
            return
         fi
      fi
   fi
   
   #check for sntl-server
   a="0"
   b="0"
   c="0"
   tmp="0"
   OLD_SERVER_RPM=`rpm -q sntl-server`
   if [ $? -eq 0 ]
   then
      tmp=`echo $OLD_SERVER_RPM|cut -d - -f 3`
      a=`echo $tmp|cut -d . -f 1`
      b=`echo $tmp|cut -d . -f 2`
      c=`echo $tmp|cut -d . -f 3`
      tmp=$a$b$c
      if [ $tmp -eq $SRV_VER ]
      then
         OLD_SERVER_RET=1
         return
      elif [ $tmp -lt $SRV_VER ]
      then
         OLD_SERVER_RET=2
         return
      else
         OLD_SERVER_RET=3
         return
      fi
   fi

}

check_daemon()
{
   OLD_DAEMON_RET=0
   OLD_DAEMON_VER=0
   OLD_DAEMON_RPM=0
   
   if [ -f "$sntl_product_info" ]
   then
      OLD_DAEMON_VER=`grep sud $sntl_product_info|cut -d = -f 2|cut -d : -f 2`
      OLD_DAEMON_RPM=`grep sud $sntl_product_info|cut -d = -f 2|cut -d : -f 1`
      if [ -n "$OLD_DAEMON_VER" ]
      then
         if [ $OLD_DAEMON_VER -eq $SUD_VER ]
         then
            OLD_DAEMON_RET=1
            return
         elif [ $OLD_DAEMON_VER -lt $SUD_VER ]
         then
            OLD_DAEMON_RET=2
            return
         else
            OLD_DAEMON_RET=3
            return
         fi
      fi
   fi
   
   if [ -f "$shk_product_info" ]
   then
      OLD_DAEMON_VER=`grep sud $shk_product_info|cut -d = -f 2|cut -d : -f 2`
      OLD_DAEMON_RPM=`grep sud $shk_product_info|cut -d = -f 2|cut -d : -f 1`
      if [ -n "$OLD_DAEMON_VER" ]
      then
         if [ $OLD_DAEMON_VER -eq $SUD_VER ]
         then
            OLD_DAEMON_RET=1
            return
         elif [ $OLD_DAEMON_VER -lt $SUD_VER ]
         then
            OLD_DAEMON_RET=2
            return
         else
            OLD_DAEMON_RET=3
            return
         fi
      fi
   fi

   #check for sntl-sud
   a="0"
   b="0"
   c="0"
   tmp="0"
   OLD_DAEMON_RPM=`rpm -q sntl-sud`
   if [ $? -eq 0 ]
   then
      tmp=`echo $OLD_DAEMON_RPM|cut -d - -f 3`
      a=`echo $tmp|cut -d . -f 1`
      b=`echo $tmp|cut -d . -f 2`
      c=`echo $tmp|cut -d . -f 3`
      tmp=$a$b$c
      if [ $tmp -eq $SUD_VER ]
      then
         OLD_DAEMON_RET=1
         return
      elif [ $tmp -lt $SUD_VER ]
      then
         OLD_DAEMON_RET=2
         return
      else
         OLD_DAEMON_RET=3
         return
      fi
   fi
   
   #check for ssp630
   rpm -q ssp630 > /dev/null 2>&1
   if [ $? -eq 0 ]
   then
      OLD_DEMON_RET=4
      return
   fi
   
   #check for ssp631
   rpm -q ssp631 > /dev/null 2>&1
   if [ $? -eq 0 ]
   then
      OLD_DEMON_RET=5
      return
   fi

   #check for ssp700
   rpm -q SSP-Toolkit-RH9 > /dev/null 2>&1
   if [ $? -eq 0 ]
   then
      OLD_DEMON_RET=6
      return
   fi

}

ifcontinue()
{
	IfContinue="a"
	while [ $IfContinue != "n" ] && [ $IfContinue != "y" ]
	do
		echo "Continue installing Sentinel Protection Installer $spi_version will uninstall old version Sentinel System Driver or Sentinel Protection Server"
		echo -n "Do you want to continue (y/n):"
		read IfContinue
		if [ $IfContinue = "n" ] || [ $IfContinue = "y" ]
		then
			break
		else
			IfContinue="a"
		fi
	done

}


uninstall630()
{
   pkg1=`rpm -q sspmontool-RH9 |cut -b 1`
   if [ $pkg1 = 's' ]
   then
      echo "Uninstalling previous MonitoringTool."
      (rpm -e sspmontool-6.3.0) >/dev/null 2>&1
      echo $line
   fi

   pkg1=`rpm -q sspserver-RH9 |cut -b 1`
   if [ $pkg1 = 's' ]
   then
      echo "Uninstalling previous SSP Server."
      (rpm -e  sspserver-6.3.0) > /dev/null 2>&1
      if [ $? = 0 ]
      then
         ((ls /opt/RainbowTechnologies/SuperPro/6.3.0/ | grep Dev > /dev/null 2>&1) ||
         (ls /opt/RainbowTechnologies/SuperPro/6.3.0/ | grep Interfaces > /dev/null 2>&1) ||
         (ls /opt/RainbowTechnologies/SuperPro/6.3.0/ | grep Manuals > /dev/null 2>&1) ||
         (ls /opt/RainbowTechnologies/SuperPro/6.3.0/ | grep Sample > /dev/null 2>&1) ||
         (ls /opt/RainbowTechnologies/SuperPro/6.3.0/ | grep Tools > /dev/null 2>&1))
         if [ $? != 0 ]
         then
            (ls /opt/RainbowTechnologies | grep SUD > /dev/null 2>&1)
            if [ $? != 0 ]
            then
               rm -rf /opt/RainbowTechnologies
            else
               rm -rf /opt/RainbowTechnologies/SuperPro
            fi
         fi
      fi
      echo $line
   fi

}

uninstall631()
{
   pkg1=`rpm -q sspmontool |cut -b 1`
   if [ $pkg1 = 's' ]
   then
      echo "Uninstalling previous MonitoringTool."
      (rpm -e sspmontool) >/dev/null 2>&1
      echo $line
   fi

   pkg1=`rpm -q sspserver |cut -b 1`
   if [ $pkg1 = 's' ]
   then
      echo "Uninstalling previous SSP Server."
      (rpm -e  sspserver) > /dev/null 2>&1
      if [ $? = 0 ]
      then
         ((ls /opt/RainbowTechnologies/SuperPro/6.3/ | grep Dev > /dev/null 2>&1) ||
         (ls /opt/RainbowTechnologies/SuperPro/6.3/ | grep Interfaces > /dev/null 2>&1) ||
         (ls /opt/RainbowTechnologies/SuperPro/6.3/ | grep Manuals > /dev/null 2>&1) ||
         (ls /opt/RainbowTechnologies/SuperPro/6.3/ | grep Sample > /dev/null 2>&1) ||
         (ls /opt/RainbowTechnologies/SuperPro/6.3/ | grep Tools > /dev/null 2>&1))
         if [ $? != 0 ]
         then
            (ls /opt/RainbowTechnologies | grep SUD > /dev/null 2>&1)
            if [ $? != 0 ]
            then
               rm -rf /opt/RainbowTechnologies
            else
               rm -rf /opt/RainbowTechnologies/SuperPro
            fi
         fi
      fi
      echo $line
   fi

   #Remove the combo_uninstall.sh i.e. this script
   (ls /opt/RainbowTechnologies/SuperPro/6.3/combo_uninstall.sh) > /dev/null 2>&1
   if [ $? == 0 ]
   then
      (rm -f /opt/RainbowTechnologies/SuperPro/6.3/combo_uninstall.sh) > /dev/null 2>&1
   fi

}

##########################################################################################
#                      START HERE
##########################################################################################
clear
line="-------------------------------------------------------------------------------"
LicensePATH=.
LicenseSHOW=0
ret=0
Package=""

check_dep
check_OS

# showlicense
# showbanner

LicenseSHOW=1
export LicenseSHOW

choice=-1
val=0

# showmenu
# if [ "$choice" = "0" ]
# then
#    exit
# fi

check_server
if [ $OLD_SERVER_RET -eq 1 ]
then
   echo "Same version sntl-server detected." >/dev/null 2>&1
elif [ $OLD_SERVER_RET -eq 2 ] # lower version exists
then
   echo "Installer has detected a lower version of Sentinel Protection Server on your system"
   ifcontinue
   if [ $IfContinue = "n" ]
   then
      echo "Installer aborted by user."
      exit
   fi
   
   rpm -q $OLD_SERVER_RPM >/dev/null 2>&1
   if [ $? -eq 0 ]
   then
      rpm -e --allmatches --nodeps $OLD_SERVER_RPM >/dev/null 2>&1
   fi
elif [ $OLD_SERVER_RET -eq 3 ] # higher version exists
then
   echo "Installer has detected a higher version of Sentinel Protection Server on your system"
	echo "Aborting Installation of $PRODUCT_NAME $version..."
	exit
fi

check_daemon
if [ $OLD_DAEMON_RET -eq 0 ]
then
   echo "No previous version sud detected." >/dev/null 2>&1
elif [ $OLD_DAEMON_RET -eq 1 ] # same version exists
then
   echo "Same version sud detected." >/dev/null 2>&1
elif [ $OLD_DAEMON_RET -eq 2 ] # lower version with record exists
then
   echo "Installer has detected a lower version of Sentinel System Driver on your system."
   ifcontinue
   if [ $IfContinue = "n" ]
   then
      echo "Installer aborted by user."
      exit
   fi
   rpm -q $OLD_DAEMON_RPM >/dev/null 2>&1
   if [ $? -eq 0 ]
   then
      rpm -e --allmatches --nodeps $OLD_DAEMON_RPM >/dev/null 2>&1
   fi
elif [ $OLD_DAEMON_RET -eq 3 ] # higher version exists
then
   echo "Installer has detected a higher version of Sentinel System Driver on your system."
   echo "This version Sentinel System Driver will not install successfully."
else # other
   echo "Installer has detected a lower version of Sentinel System Driver on your system"
   ifcontinue
   if [ $IfContinue = "n" ]
   then
      echo "Installer aborted by user."
      exit
   fi
   
   # uninstall old sud
   uninstall630
   uninstall631
fi

# Install sud 
cd ./driver
sh sud_install.sh nobanner

# Install server
cd ..
check=`ps -e|grep spnsrvlnx|cut -c 1-5`
if [ "$check" != "" ]
then
   kill -9 $check >/dev/null 2>&1 # stop server
fi
rpm -Uvh --force "server/$SERVER_RPM-$srv_version-0.i386.rpm"

# Copy ReadMe.pdf and protection_uninstall.sh
rpm -q $SPI_RPM-$version > /dev/null 2>&1
if [ $? -ne 0 ]
then
   mkdir -p $SPI_INSTALL_DIR/help >/dev/null 2>&1
   install -D -m 644 ./help/ReadMe.pdf $SPI_INSTALL_DIR/help/ReadMe.pdf >/dev/null 2>&1
   install -D -m 755 ./protection_uninstall.sh $PACKAGE_DIR/$PRODUCT_NAME/$MAJOR.$MINOR/protection_uninstall.sh >/dev/null 2>&1
   
   FILESTAMP=$RELEASE_DATE"0"$SPI_MAJOR"0"$SPI_MINOR".0"$SPI_REV;
   find $SPI_INSTALL_DIR -exec touch -t $FILESTAMP {} \;
   touch -t $FILESTAMP $PACKAGE_DIR/$PRODUCT_NAME/$MAJOR.$MINOR/protection_uninstall.sh
   
   FILESTAMP=$RELEASE_DATE"0"$MAJOR"0"$MINOR".0"$REV;
   touch -t $FILESTAMP $PACKAGE_DIR/$PRODUCT_NAME/$MAJOR.$MINOR
fi

