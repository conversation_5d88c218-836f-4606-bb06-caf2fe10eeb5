#!/bin/sh
################################################################################
#                    Copyright (C) 2015 SafeNet, Inc.                          #
#                         All Rights Reserved                                  #
#                                                                              #
# server_install.sh : This install Sentinel Protection Server version 7.6.1    #
#                                                                              #
################################################################################

#
# check for dependency
#
check_dep()
{
   #check for root
   user_id=`id -u`
   if [ $user_id -ne 0 ]
   then
      echo "ERROR: To install this software make sure you have Super User (root) permission."  
      echo "Installation aborted."
      exit
   fi

}

#
# show Sentinel Protection Server banner
#
showbanner()
{
   echo $line
   echo "         Sentinel Protection Server 7.6.1 (for Debian and Ubuntu) Installation Script"
   echo "                    Copyright (C) 2015 SafeNet, Inc."
   echo "                         All rights reserved."
   echo $line
   echo "This script will install Sentinel Protection Server 7.6.1:"

}

#
# START HERE
#
clear
line="-------------------------------------------------------------------------------"

check_dep
showbanner

#check for old sntl-sud
dpkg -l |grep sntl-server |grep "7.1.1" >/dev/null 2>&1
if [ $? -eq 0 ]
then
	echo "\"Old version Sentinel Protection Server has already installed on the system! Now uninstall it firstly...\""	
	dpkg -P sntl-sud
	echo "\"Old version Sentinel Protection Server has been uninstalled from the system.\""
fi

dpkg -i --force-architecture sntl-server_7.6.1-0_i386.deb 2>/dev/null
