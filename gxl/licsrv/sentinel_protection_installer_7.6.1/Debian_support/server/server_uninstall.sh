#!/bin/sh
################################################################################
#                    Copyright (C) 2015 SafeNet, Inc.                          #
#                         All Rights Reserved                                  #
#                                                                              #
# server_uninstall.sh : This uninstall Sentinel Protection Server version 7.6.1#
#                                                                              #
################################################################################

#
# check for dependency
#
check_dep()
{
   #check for root
   user_id=`id -u`
   if [ $user_id -ne 0 ]
   then
      echo "ERROR: To install this software make sure you have Super User (root) permission."  
      echo "Installation aborted."
      exit
   fi

}


#
# show Sentinel Protection Server banner
#
showbanner()
{
   echo $line
   echo "         Sentinel Protection Server 7.6.1 (for Debian and Ubuntu) Uninstallation Script"
   echo "                    Copyright (C) 2015 SafeNet, Inc."
   echo "                         All rights reserved."
   echo $line
   echo "This script will uninstall Sentinel Protection Server 7.6.1:"

}

#
# START HERE
#
clear
line="-------------------------------------------------------------------------------"

check_dep
showbanner

dpkg -l |grep sntl-server >/dev/null 2>&1
if [ $? -ne 0 ]
then
   sleep 1
   echo "\"Sentinel Protection Server is not installed on the system!\""	
   exit 0
fi

dpkg -l |grep sntl-server:i386 > /dev/null 2>&1
if [ $? -eq 0 ]
then
	dpkg -P sntl-server:i386 2>/dev/null
else
	dpkg -P sntl-server 2>/dev/null
fi