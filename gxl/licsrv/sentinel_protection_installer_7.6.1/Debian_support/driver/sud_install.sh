#!/bin/sh
################################################################################
#                    Copyright (C) 2015 SafeNet, Inc.                          #
#                         All Rights Reserved                                  #
#                                                                              #
# sud_install.sh : Install Sentinel USB Daemon 7.5.4 on Debian/Ubuntu       #
#                                                                              #
################################################################################



################################################################################
# Check for Dependencies
################################################################################
check_dep()
{
	#check for root
	user_id=`id -u`
	if [ $user_id -ne 0 ]
	then
		echo "ERROR:To Install this software make sure you have Super User (root) permission."  
		echo "Installation Aborted."
		exit
	fi

}


################################################################################
# Show Banner
################################################################################
showbanner()
{
	echo $line
	echo "         Sentinel USB Daemon 7.5.4 (for Debian and Ubuntu) Installation Script"
	echo "                    Copyright (C) 2015 SafeNet, Inc."
	echo "                         All rights reserved."
	echo $line
	echo "This script will install Sentinel USB Daemon 7.5.4:"

}


################################################################################
# START HERE
################################################################################
clear
line="-------------------------------------------------------------------------------"
showbanner
check_dep

#check for old sntl-sud
dpkg -l |grep sntl-sud |grep "7.[3|4].0" > /dev/null 2>&1
if [ $? -eq 0 ]
then
	echo "\"Old version USB Daemon has already installed on the system! Now uninstall it firstly...\""	
	dpkg -P sntl-sud
	echo "\"Old version USB Daemon has been uninstalled from the system.\""
fi

dpkg -i --force-architecture sntl-sud_7.5.4-0_i386.deb 2>/dev/null

