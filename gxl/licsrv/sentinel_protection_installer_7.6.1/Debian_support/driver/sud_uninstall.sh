################################################################################
#                    Copyright (C) 2015 SafeNet, Inc.                          #
#                         All Rights Reserved                                  #
#                                                                              #
# sud_uninstall.sh : Uninstall Sentinel USB Daemon 7.5.4 on Debian/Ubuntu     #
#                                                                              #
################################################################################
#!/bin/sh


################################################################################
# Check for Dependencies
################################################################################
check_dep()
{
	#check for root
	user_id=`id -u`
	if [ $user_id -ne 0 ]
	then
		echo "ERROR:To Uninstall this software make sure you have Super User (root) permission."  
		echo "Uninstallation Aborted."
		exit
	fi

}


################################################################################
# Show Sentinel Keys USB Daemon Banner
################################################################################
showbanner()
{
	echo $line
	echo "         Sentinel USB Daemon 7.5.4 (for Debian and Ubuntu) Uninstallation Script"
	echo "                    Copyright (C) 2015 SafeNet, Inc."
	echo "                         All rights reserved."
	echo $line
	echo "This script will uninstall Sentinel USB Daemon 7.5.4:"

}


################################################################################
# START HERE
################################################################################
clear
line="-------------------------------------------------------------------------------"
showbanner
check_dep

dpkg -l |grep sntl-sud > /dev/null 2>&1
if [ $? -ne 0 ]
then
	sleep 1
	echo "\"Sentinel USB Daemon is not installed on the system!\""	
	exit 0
fi

dpkg -l |grep sntl-sud:i386 > /dev/null 2>&1
if [ $? -eq 0 ]
then
	dpkg -P sntl-sud:i386 2>/dev/null
else
	dpkg -P sntl-sud 2>/dev/null
fi