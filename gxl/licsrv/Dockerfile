FROM centos:centos7
#.0.1406
# COPY LicenseServer-8.6.1-centos7.0.1406-x86_64.rpm /tmp/
COPY rpm/ /tmp/rpm/

# COPY glibc-common-2.17-106.el7_2.8.x86_64.rpm /tmp/
# COPY glibc-2.17-106.el7_2.8.x86_64.rpm /tmp/
# COPY nss-softokn-freebl-********-14.2.el7_2.x86_64.rpm /tmp/
WORKDIR /tmp/rpm

RUN yum -y install glibc.i686 && yum clean all
RUN rpm -ivh --force --nodeps Licenseserver-8.6-1-centos7.x86_64.rpm

# ARG SERVICE_BIN=/usr/local/srms/bin/lserv
# ARG SERVICE_ARGS="-s /usr/local/srms/bin/lservrc"

WORKDIR /tmp
COPY lservrc /tmp/
COPY sentinel_protection_installer_7.6.1/ /tmp/sentinel_protection_installer_7.6.1/



WORKDIR /tmp/sentinel_protection_installer_7.6.1
RUN chmod u+x protection_install.sh && ./protection_install.sh

ENV LSHOST=127.0.0.1
ENV SERVICE_BIN=/usr/local/srms/bin/lserv
ENV SERVICE_ARGS="-s /usr/local/srms/bin/lservrc"
COPY lservrc_lf /tmp/lservrc

COPY start.sh /tmp/
RUN chmod u+x /tmp/start.sh
# RUN yum -y install initscripts && yum clean all

# RUN rpm -ivh --nodeps *.rpm
# RUN ./LicenseServer-8.6.1-centos7.0.1406-x86_64.rpm