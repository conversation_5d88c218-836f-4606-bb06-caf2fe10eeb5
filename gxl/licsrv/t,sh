#!/bin/sh
USBDAEMON_INSTALL_DIR=/opt/safenet_sentinel/common_files/sentinel_usb_daemon
USBDAEMON_DIR32=32Bit
USBDAEMON_DIR64=64Bit
old_sntl_product_info=/var/opt/sentinel/sntl_product.info
progname="usbdaemon"
PROG_NAME=/opt/safenet_sentinel/common_files/sentinel_usb_daemon/usbdaemon
#USBDAEMON_INSTALL_DIR=/opt/sentinel/sud/usb

# check whether the executable file is present or not
[ -f "$USBDAEMON_INSTALL_DIR/$progname" ] || exit 1

user_id=`id -u`
if [ $user_id -ne 0 ]
then
	echo "ERROR: To run this script make sure you have Super User (root) permission."  
	exit
fi

OS=other
found=0

for OSL in SUSE Debian Ubuntu
do
	if [ $found -eq 0 ]
	then
		cat /etc/issue |grep $OSL > /dev/null 2>&1
		if [ $? -eq 0 ]
		then
			OS=$OSL
			found=1
		fi
fi
done

if [ "$OS" = "Ubuntu" ]
then
	OS=Debian
fi

case $OS in
SUSE)
	. /etc/rc.status
	;;
Debian)
	. /lib/lsb/init-functions
	;;
*)
    . /etc/init.d/functions
	;;
esac


create_cron()
{
	/usr/bin/crontab -l >/tmp/sntltmp.cron
	echo "00 10 * * * /bin/touch /tmp/u.daemon # Sentinel" >>/tmp/sntltmp.cron
	/usr/bin/crontab /tmp/sntltmp.cron
        rm -f /tmp/sntltmp.cron
}
#
# Create links to support old clients(older then ultrapro 1.0)
#
create_links()
{
	echo "Adding support for previous releases..."
	#create links for socket file to support clients earlier then SSP 7.0.0    
	rm -f /tmp/u.daemon > /dev/null 2>&1
	ln -s /var/run/sentinel/u.daemon  /tmp/u.daemon > /dev/null 2>&1 	
	if [ $? -eq 0 ]
	then
		chmod 666 /tmp/u.daemon > /dev/null 2>&1
		
		mkdir -p /opt/RainbowTechnologies/SUD5.5.0/USB > /dev/null 2>&1
		chmod 777 /opt/RainbowTechnologies/SUD5.5.0/USB > /dev/null 2>&1
		rm -f /opt/RainbowTechnologies/SUD5.5.0/USB/u.daemon  > /dev/null 2>&1
		ln -s /var/run/sentinel/u.daemon /opt/RainbowTechnologies/SUD5.5.0/USB/u.daemon > /dev/null 2>&1
		if [ $? -eq 0 ]
		then	
			chmod 666 /opt/RainbowTechnologies/SUD5.5.0/USB/u.daemon > /dev/null 2>&1
			echo "Done."
		else 
			echo "Failed."
		fi
	else
		echo "Failed."
    fi
    create_cron
}

#
# Remove Symbolic links created for supporting earlier versions
#
remove_links()
{
	if [ -L /tmp/u.daemon ] ; then
		rm -f /tmp/u.daemon  > /dev/null 2>&1
	fi
	if [ -L /opt/RainbowTechnologies/SUD5.5.0/USB/u.daemon ] ; then
		rm -f /opt/RainbowTechnologies/SUD5.5.0/USB/u.daemon   > /dev/null 2>&1
		ret=`ls /opt/RainbowTechnologies/SUD5.5.0/USB`
		if [ -z $ret ] ; then
			rm -rf /opt/RainbowTechnologies/SUD5.5.0/USB > /dev/null 2>&1
		fi
		ret=`ls /opt/RainbowTechnologies/SUD5.5.0`
		if [ -z $ret ] ; then
			rm -rf /opt/RainbowTechnologies/SUD5.5.0 > /dev/null 2>&1
		fi
		ret=`ls /opt/RainbowTechnologies`
		if [ -z $ret ] ; then
			rm -rf /opt/RainbowTechnologies > /dev/null 2>&1
		fi
	fi
}

#
# Start USB Daemon
#
start()
{
    mkdir -p /var/run/sentinel > /dev/null 2>&1
    chmod 775 /var/run/sentinel > /dev/null 2>&1
	chmod +x $PROG_NAME > /dev/null 2>&1

#	sh $0 status | grep "is running" > /dev/null 2>&1
	ps -el | grep usbdaemon > /dev/null 2>&1
   if [ $? -eq 0 ]
   then
        echo "USB Daemon already running!!"
   else
		echo -n "Starting Sentinel USB Daemon:"
		
		case $OS in
		SUSE)
			startproc $PROG_NAME
        rc_status -v
			;;
		Debian)
			start-stop-daemon --start --exec $PROG_NAME
			;;
		*)
                 daemon $PROG_NAME
			;;
		esac
        fi


        RETVAL=$?

        #create links for socket file to support old clients
        [ -f "$old_sntl_product_info" ]
        if [ $? -eq 0 ]
        then
        tmp_var_product=`grep -c product $old_sntl_product_info|cut -d = -f 2|cut -d : -f 2`
        if [ -n "$tmp_var_product" ]
        then
                if [ $tmp_var_product -eq 1 ]
                then
                create_links > /dev/null 2>&1
                chmod 666 /var/run/sentinel/u.daemon > /dev/null 2>&1
                fi
        fi
        fi
        
	if [ -f /tmp/u.daemon ]
	then
        create_cron
	fi

        if [ -f /etc/redhat-release ]
        then
                echo
                return $RETVAL
        fi
}

#
# Stop USB Daemon
#
stop()
{
	sh $0 status |grep not >/dev/null 2>&1
	if [ $? -eq 0 ]
	then
		echo "USB Daemon is not running!!"
	else
		echo -n "Stopping Sentinel USB Daemon: "

cd $USBDAEMON_INSTALL_DIR/

		#Check the Distribution
		case $OS in
		SUSE)
   killproc $PROG_NAME
   rc_status -v
			;;
		Debian)
			pkill -KILL usbdaemon >/dev/null 2>&1
			;;
		*)
   killproc ./usbdaemon
			;;
		esac

    remove_links > /dev/null 2>&1
    rm -rf /var/run/sentinel > /dev/null 2>&1

    if [ -f /etc/redhat-release ]
    then
            RETVAL=$?
            echo
            return $RETVAL
    fi
	fi
}

#
# status function
#
status()
{
	ps -el | grep usbdaemon > /dev/null 2>&1
	if [ $? -eq 0 ]
	then
		echo "\"USB daemon is running!\""
	else
		echo "\"USB daemon is not running!\""
	fi
}

#
# Restart USB Daemon
#
restart(){
    stop    
    start
}


#
#  Start Here
#
case "$1" in
    start)
	start
	;;
    stop)
	stop
	;;
    status)
	status
	;;
    restart)
	restart
	;;
	support)
	create_links
	;;
    *)
	echo $"Usage: $0 {start|stop|status|restart|support}"
	RETVAL=1
esac

#Check the Distribution
cat /etc/issue |grep -i SUSE >/dev/null
if [ $? -eq 0 ]
then
rc_exit
fi
exit $RETVAL
