

--------------------------------------------------------------------------------
-- Copyright (c) 2008. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.
--------------------------------------------------------------------------------
--
-- Sc<PERSON><PERSON> to add footprint components into JPS database
--
-- Run this script by Postgres  pgAdmin tool ->Query tool->Execute Query. 
--
--------------------------------------------------------------------------------

BEGIN;

-------------------------
-- Table:  scene_catalog
-------------------------
CREATE TABLE scene_catalog
(
    id             serial    NOT NULL,
    scene_id       text      NOT NULL,
    footprint      polygon   NULL,
    color          text      NULL,
    scene_state    text      NULL,
    last_update    timestamp with time zone NULL,
    descr          text      NULL,
    job_id         integer   NULL,
    master_job_id  integer   NULL,
    CONSTRAINT pk_scene_catalog PRIMARY KEY (id),
    CONSTRAINT udx_user_id UNIQUE (master_job_id, scene_id)
);


COMMENT ON TABLE scene_catalog IS
'Table to store information about scene processing like footprint, description, status, etc.';

COMMENT ON COLUMN scene_catalog.id IS 'Auto generated id';
COMMENT ON COLUMN scene_catalog.scene_id IS 'Unique scene id';
COMMENT ON COLUMN scene_catalog.footprint IS
'Sequence of point (x1,y1),(x2,y2),.. where x-longitude, y-latitude';
COMMENT ON COLUMN scene_catalog.color IS 'RGB color like FF0000';
COMMENT ON COLUMN scene_catalog.scene_state IS
'Scene processing stale like pansharpening, mosaicing';
COMMENT ON COLUMN scene_catalog.last_update IS
'Datetime of last update of this record';
COMMENT ON COLUMN scene_catalog.descr IS 'Any description';
COMMENT ON COLUMN scene_catalog.job_id IS
'Id of JPS job which is currently processing this scene';
COMMENT ON COLUMN scene_catalog.master_job_id IS
'Id of JPS job which is root master for job in job_id column';


---------------------------------
-- Function: scene_catalog_audit
---------------------------------
CREATE OR REPLACE FUNCTION scene_catalog_audit()
  RETURNS trigger AS
$BODY$
BEGIN
    new.LAST_UPDATE := now();
    return new;
END;
$BODY$
LANGUAGE 'plpgsql'; 

COMMENT ON FUNCTION scene_catalog_audit() IS
'Trigger function to set last update datetime to scene_catalog table';


------------------------------
-- Function: parse_footprint
-- This trigger must be the last trigger on the LOGS table, otherwise
-- all other triggers will fail.  This trigger returns null when it
-- successfully updates the SCENE_CATALOG table.
------------------------------
CREATE OR REPLACE FUNCTION parse_footprint()
  RETURNS trigger AS
  $BODY$
  DECLARE
    split text[];
    keysplit text[];
    len integer;
    vCommand text;
    vSceneid text;
    vFootprint polygon;
    vState text;
    vColor text;
    vJobid integer;
    vDescr text;
    vMasterJobId integer;
    fl integer := 0;
  BEGIN

    if new.category != 'jps.footprint' then
        return new;
    end if;
    
    if new.message = null or length(new.message) = 0 then
        return new;
    end if; 
    
    split := regexp_split_to_array(new.message, E';');

    len := array_upper(split, 1);
    
    if len = 0 then
      return new;
    end if;
     
    for i in 1..len loop

      keysplit :=  regexp_split_to_array(split[i], E'=');
      if array_upper(keysplit,1) != 2 then
          return new;
      end if;

      if keysplit[1] = 'COMMAND' then
          vCommand := keysplit[2];
      elsif keysplit[1] = 'SCENEID' then
          vSceneid := keysplit[2];
      elsif keysplit[1] = 'BBOX' then 
          vFootprint := keysplit[2];     
      elsif keysplit[1] = 'COLOR' then 
          vColor := keysplit[2];     
      elsif keysplit[1] = 'STATE' then 
          vState := decode_param(keysplit[2]);     
      elsif keysplit[1] = 'DESCR' then 
          vDescr := decode_param(keysplit[2]);     
      end if;
    END LOOP; 
    
    vJobId := new.job_id;
    
    if (vJobId is not null) then        
        vMasterJobId := find_root_job(vJobId);
    end if;    

    if vCommand = 'FOOTPRINT' then
        select 1 
        into fl 
        from scene_catalog
        where scene_id = vSceneid and
              (  master_job_id = vMasterJobId or 
                 master_job_id is null and vSceneid is null
              );

        if fl = 1 then 
            update scene_catalog set
              footprint = vFootprint,
              color = vColor,
              scene_state = vState,
              descr = vDescr,
              job_id =vJobid
            where     
              scene_id = vSceneid and
              (  master_job_id = vMasterJobId or 
                 master_job_id is null and vSceneid is null
              );
            new.message = 'footprint for scene id='||vSceneid||' updated';
        else
            insert into scene_catalog
              ( scene_id, footprint, color, scene_state,
                descr, job_id, master_job_id )
            values
              ( vSceneid, vFootprint, vColor, vState,
                vDescr, vJobid, vMasterJobId );
            new.message = 'footprint for scene id='||vSceneid||' created';
         end if;
         
     elsif vCommand = 'MESSAGE' then 
        select 1 
        into fl 
        from scene_catalog
        where scene_id = 'MESSAGE' and
              (  master_job_id = vMasterJobId or 
                 master_job_id is null and vSceneid is null
              );

        if fl = 1 then 
            update scene_catalog set
              descr = vDescr,
              job_id =vJobid
            where     
              scene_id = 'MESSAGE' and
              (  master_job_id = vMasterJobId or 
                 master_job_id is null and vSceneid is null
              );
            new.message = 'footprint message updated';
        else
            insert into scene_catalog
              (scene_id, descr, job_id, master_job_id  )
            values
              ('MESSAGE', vDescr, vJobid, vMasterJobId );
              
            new.message = 'footprint message created';
         end if;

     elsif vCommand = 'DELETE' then 
         if vSceneid is not null and length(vSceneid) > 0 then 
             delete from scene_catalog 
             where scene_id = vSceneid and
                  (  master_job_id = vMasterJobId or 
                     master_job_id is null and vSceneid is null
                  );
  
             new.message = 'footprint for scene id='||vSceneid||' deleted';
         else
             delete from scene_catalog; 
             new.message = 'all footprints deleted';
         end if;       
         
     end if;

     -- Do not add the message to the LOGS table.  return new if you want to
     -- add the message to the LOGS table.
     return new;
  END;
  $BODY$
  LANGUAGE 'plpgsql'; 

COMMENT ON FUNCTION parse_footprint() IS
'Function for trigger on logs table to intercept log records with jps.footprint category, parse them and redirect to scene_catalog table';


------------------------------
-- Function: find_root_job
------------------------------
  CREATE OR REPLACE FUNCTION find_root_job(in jobId integer)
    RETURNS integer AS
  $BODY$
  DECLARE
    vParentJobId integer;
    str text;
  BEGIN
      if jobId is null then
          return null;
      end if;
      
      select parent_job_id
      into vParentJobId
      from jobs
      where job_id = jobId;
      
      if vParentJobId is null then
          return jobId;
      end if;
      
      return find_root_job(vParentJobId);
  END;
  $BODY$
  LANGUAGE 'plpgsql';  

COMMENT ON FUNCTION find_root_job(integer) IS
'Function to search for the root JPS job for a given job';


------------------------------
-- Function: decode_param
------------------------------
CREATE OR REPLACE FUNCTION decode_param(in str text)
  RETURNS text AS
$BODY$
DECLARE
    res text;
BEGIN
    if str is null or length(str) = 0 then
        return null;
    end if;
    res := replace(str, '%3B', ';');
    res := replace(res, '%3D', '=');     
    res := replace(res, '%5C', E'\\');     
    res := replace(res, '%60', E'\'');     
    return res;
END;
$BODY$
LANGUAGE 'plpgsql';  

COMMENT ON FUNCTION decode_param(text) IS
'Function to decode escaped symbols in a string';


-- ==========
-- Triggers 
-- ==========
CREATE TRIGGER scene_catalog_audit
  BEFORE INSERT OR UPDATE
  ON scene_catalog
  FOR EACH ROW
  EXECUTE PROCEDURE scene_catalog_audit();


CREATE TRIGGER footprint_trg
  BEFORE INSERT OR UPDATE
  ON logs
  FOR EACH ROW
  EXECUTE PROCEDURE parse_footprint();

END;


