﻿-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/workflows/JPS/py/Level5Production.pyc', 1,
  'Level5Production', '正射到镶嵌',
  '从正射卫星影像生成镶嵌影像产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'Level5ProductionParameters', null);
