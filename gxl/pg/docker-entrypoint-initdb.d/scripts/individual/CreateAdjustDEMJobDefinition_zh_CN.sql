﻿-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/AdjustDEMJob.pyc', 1,
  'AdjustDEMJob', '修正DEM',
  '使用三维控制点或加密点修正DEM。',
  50, 'GXLWorkflow', 'PrmXmlType', 'AdjustDEMJobParameters', null);
 
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/AdjustDEMChildJob.pyc', 15,
  'AdjustDEMChildJob', '修正DEM子作业',
  '使用三维控制点或加密点修正DEM。',
  50, 'GXLWorkflow', 'PrmXmlType', 'AdjustDEMChildJobParameters', null);
