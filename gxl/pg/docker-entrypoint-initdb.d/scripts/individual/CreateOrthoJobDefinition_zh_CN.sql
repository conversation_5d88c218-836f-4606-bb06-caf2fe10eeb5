-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/Orthorectification/JPS/py/MasterOrtho.pyc', 1,
  'GXLMasterOrtho', '正射校正',
  '影像正射校正，生成正射产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'OrthoParameters',
  '正射校正主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Orthorectification/JPS/py/Ortho.pyc', 21,
  'GXLOrtho', '正射校正子作业',
  '对指定的影像正射校正，生成正射产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'OrthoParameters',
  '正射校正子作业参数');
