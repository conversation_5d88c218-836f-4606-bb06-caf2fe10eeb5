﻿-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/workflows/JPS/py/DEMProduction.pyc', 1,
  'DEMProduction', '从原始影像生产DEM',
  '从供应商提供的原始卫星影像生成地理编码的DEM和DTM产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DEMProductionParameters', null);
