-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/Mosaicking/MosaicGeneration/JPS/py/DEMMosaicGeneration.pyc',
  1, 'DEMMosaicGeneration', 'DEM镶嵌',
  '生成DEM镶嵌的所有瓦块。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DEMMosaicGenerationParameters',
  'DEM镶嵌作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Mosaicking/MosaicGeneration/JPS/py/MosaicGeneration.pyc', 21,
  'DEMMosaicGenerationChild', 'DEM镶嵌子作业',
  '生成DEM镶嵌瓦块。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DEMMosaicGenerationParameters',
  'DEM镶嵌子作业参数');

