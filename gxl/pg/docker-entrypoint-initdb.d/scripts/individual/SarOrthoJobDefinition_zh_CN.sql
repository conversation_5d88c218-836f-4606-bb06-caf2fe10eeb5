-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/Orthorectification/JPS/py/SarMasterOrtho.pyc', 1,
  'MasterSarOrtho', '雷达正射校正',
  '对包含有效数学模型的雷达影像正射校正进行。',
  50, 'GXLWorkflow', 'PrmXmlType', 'OrthoParameters',
  '正射校正作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Orthorectification/JPS/py/SarOrtho.pyc', 21,
  'SarOrtho', '雷达正射校正子作业',
  '对包含有效数学模型的雷达影像正射校正进行。',
  50, 'GXLWorkflow', 'PrmXmlType', 'OrthoParameters',
  '正射校正作业参数');
