-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/RasterToPolygon/JPS/py/MasterGenerationJob.pyc',
  1.0, 'MasterRasterToPolygonGeneration', '光栅到多边形',
  '从影像中提取多边形。',
  50.0, 'GXLWorkflow','PrmXmlType','RasterToPolygonGenerationParameters',null);
SELECT install_job (
  '${GXL_ROOT}/PGS/RasterToPolygon/JPS/py/GenerationJob.pyc',
  40.0, 'RasterToPolygonGeneration', '光栅到多边形子作业',
  '从单一图像中提取多边形。',
  50.0, 'GXLWorkflow','PrmXmlType', 'RasterToPolygonGenerationParameters',null);
