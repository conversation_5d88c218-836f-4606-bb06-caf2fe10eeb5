-- Copyright (c) 2017. All rights reserved  -  PCI Geomatics
-- 490 <PERSON>-Joseph,Suite 400, Hull, QUE, Canada
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/ConvertDSMToDTMJob.pyc', 1,
  'ConvertDSMToDTMJob', 'DEM转换(DSM到DTM)',
  '由DSM产生DTM',
  50, 'GXLWorkflow', 'PrmXmlType', 'ConvertDSMToDTMJobParameters', null);

SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/ConvertDSMToDTMChildJob.pyc', 25,
  'ConvertDSMToDTMChildJob', 'DEM转换(DSM到DTM)子作业',
  '由单一DSM产生单一DTM',
  50, 'GXLWorkflow', 'PrmXmlType', 'ConvertDSMToDTMChildJobParameters', null);
