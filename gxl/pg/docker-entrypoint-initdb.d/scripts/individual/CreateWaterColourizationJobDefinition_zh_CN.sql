-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/WaterColourization/JPS/py/MasterWaterColourization.pyc', 1.0,
  'GXLMasterWaterColourization', '水体着色',
  '为影像水体着色。',
  50, 'GXLWorkflow', 'PrmXmlType', 'WaterColourizationParameters',
  '水体着色作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/WaterColourization/JPS/py/WaterColourization.pyc', 15.0,
  'GXLWaterColourization', '水体着色子作业',
  '为单一影像水体着色。',
  50, 'GXLWorkflow', 'PrmXmlType', 'WaterColourizationParameters',
  '水体着色子作业参数');
