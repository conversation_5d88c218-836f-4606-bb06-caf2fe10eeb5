-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/GCPCollection/JPS/py/MasterGCPCollection.pyc', 1,
  'MasterGCPCollection', '控制点采集',
  '采集控制点。',
  50, 'GXLWorkflow', 'PrmXmlType', 'GCPCollectionParameters',
  '控制点采集主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/GCPCollection/JPS/py/GCPCollectionJob.pyc', 15,
  'GCPCollection', '控制点采集子作业',
  '为指定的影像采集控制点。',
  50, 'GXLWorkflow', 'PrmXmlType', 'GCPCollectionParameters',
  '控制点采集子作业参数');
