
-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script was written for POSTGRESQL version 8.3 and can be executed 
-- by PostgreSQL pgAdmin tool version 1.10 or higher 

-- Fill the JPS database with common information for all GXL modules.

BEGIN;

  -- Insert default user
  -- WARNING: Change this USER to improve security
  INSERT INTO users( user_name, user_password, first_name,
                     last_name, email, description, is_admin )
  SELECT 'gxladmin', 'gxladmin_password_abc',
           'GXL', 'GXL', '<EMAIL>', 
           'Admin user to run GXL jobs', TRUE WHERE NOT EXISTS
        (SELECT 1 FROM users WHERE user_name='gxladmin');


  -- Insert server configuration 
  INSERT INTO server_configs( 
     name, description, resources_capacity, jobs_per_poll_limit, web_service_url, 
     job_poll_interval_seconds, ping_interval_seconds, default_working_dir )
  SELECT 'GXLWorkflowServerConfig', 'Server Configuration for running GXL jobs',
    100.0, 100, 'http://localhost:8080/jps/job/', 1.0,  60.0, null WHERE NOT
    EXISTS (SELECT 1 FROM server_configs WHERE name='GXLWorkflowServerConfig');


  -- Insert category
  INSERT INTO
    categories( "name", description )
  SELECT 'GXLWorkflow', 'Category for GXL workflows/modules/jobs' WHERE NOT
  EXISTS (SELECT 1 FROM categories WHERE name='GXLWorkflow');

  -- Connect server config to GXL categories
  INSERT INTO server_categories (sc_id, cat_id) 
  SELECT ( SELECT sc_id FROM server_configs  WHERE name = 'GXLWorkflowServerConfig' ), 
    ( SELECT cat_id FROM categories WHERE name = 'GXLWorkflow' ) WHERE NOT EXISTS
    (SELECT 1 FROM server_categories INNER JOIN server_configs ON 
        server_categories.sc_id=server_configs.sc_id
        INNER JOIN categories ON server_categories.cat_id=categories.cat_id
        WHERE server_configs.name='GXLWorkflowServerConfig' 
        AND categories.name='GXLWorkflow');


  -- Insert PRM XML parameter definition
  INSERT INTO param_definitions( name, schema, schema_type, is_schema_remote,
     description)
  SELECT 'PrmXmlType', null, 'XMLSCHEMA', TRUE, 
    'GXL PRM XML parameter definition' WHERE NOT EXISTS
    (SELECT 1 FROM param_definitions WHERE name='PrmXmlType');

COMMIT;

