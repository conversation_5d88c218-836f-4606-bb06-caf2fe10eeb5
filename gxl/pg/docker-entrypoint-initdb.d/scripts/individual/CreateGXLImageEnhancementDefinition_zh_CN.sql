-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/ImageEnhancement/JPS/py/MasterImageEnhancement.pyc', 1,
  'GXLMasterImageEnhancement', '影像增强',
  '使用不同的增强算法对指定影像进行永久增强。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageEnhancementParameters',
  '影像增强主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/ImageEnhancement/JPS/py/ImageEnhancement.pyc', 20,
  'GXLImageEnhancement', '影像增强子作业',
  '使用不同的增强算法对指定影像进行永久增强。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageEnhancementParameters',
  '影像增强子作业参数');
