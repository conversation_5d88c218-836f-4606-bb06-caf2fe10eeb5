-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++


SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/SarDEMExtractionJob.pyc', 1,
  'SarDEMExtraction', '雷达DEM提取',
  '从重叠的雷达影像生成地理编码后的DSM和DTM。',
  50, 'GXLWorkflow', 'PrmXmlType', 'SarDEMExtractionParameters',
  '雷达DEM提取作业参数');
