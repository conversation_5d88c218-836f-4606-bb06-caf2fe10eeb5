-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/PolarimetricDecomp/JPS/py/PolarimetricDecompGenerationJob.pyc',
  1.0, 'PolarimetricDecompGeneration', '极化参数',
  '从雷达影像中生成极化参数。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'PolarimetricDecompGenerationParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/PolarimetricDecomp/JPS/py/MasterPolarimetricDecompJob.pyc',
  1.0, 'MasterPolarimetricDecomp', '极化参数主作业',
  '从原始雷达影像中生成极化参数。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'PolarimetricParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/PolarimetricDecomp/JPS/py/PolarimetricDecompJob.pyc',
  40.0, 'PolarimetricDecomp', '极化参数子作业',
  '从原始雷达影像中生成极化参数。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'PolarimetricParameters', null);
