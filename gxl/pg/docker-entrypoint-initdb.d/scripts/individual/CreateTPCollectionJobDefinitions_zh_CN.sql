-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/MasterBundleAdjustment.pyc',
  1, 'GXLMasterBundleAdjustment', '同名点采集与优化',
  '对一系列影像进行同名点采集与优化。',
  50, 'GXLWorkflow', 'PrmXmlType', 'BundleAdjustmentParameters',
  'TP作业参数');

SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/TPCollection.pyc',
  26, 'TPCollection', '同名点采集',
  '对一系列影像进行同名点采集。',
  50, 'GXLWorkflow', 'PrmXmlType', 'TPCollectionParameters',
  'TP采集子作业参数');

SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/BundleAdjustment.pyc',
  19, 'GXLBundleAdjustment', '同名点优化',
  '对所有相关影像进行同名点优化。',
  50, 'GXLWorkflow', 'PrmXmlType', 'BundleAdjustmentParameters',
  'TP作业参数');
