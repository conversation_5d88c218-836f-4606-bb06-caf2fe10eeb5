-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/DataTransformation/JPS/py/MasterDataTransformation.pyc',
  1, 'MasterDataTransformation', '空间数据转换',
  '搜寻指定目录下的所有矢量或栅格文件，创建数据格式或/和投影变换。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DataTransformationParameters',
  '空间数据转换主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DataTransformation/JPS/py/DataTransformation.pyc', 21,
  'DataTransformation', '空间数据转换子作业',
  '对指定的矢量或光栅文件进行格式或/和投影变换。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DataTransformationParameters',
  '空间数据转换子作业参数');
