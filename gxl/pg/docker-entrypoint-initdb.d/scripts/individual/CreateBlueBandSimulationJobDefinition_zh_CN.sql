-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/BlueBandSimulation/JPS/py/MasterBlueBandSimulation.pyc',
  1, 'GXLMasterBlueBandSimulation', '蓝波段模拟',
  '生成多个模拟的自然彩色图像。',
  50, 'GXLWorkflow', 'PrmXmlType', 'BlueBandSimulationParameters',
  '蓝波段模拟主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/BlueBandSimulation/JPS/py/BlueBandSimulation.pyc', 15,
  'GXLBlueBandSimulation', '蓝波段模拟子作业',
  '生成模拟自然彩色图像。',
  50, 'GXLWorkflow', 'PrmXmlType', 'BlueBandSimulationParameters',
  '蓝波段模拟子作业参数');
