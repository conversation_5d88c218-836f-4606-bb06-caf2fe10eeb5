-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/GCPCollection/JPS/py/RefImagePrepJob.pyc', 1,
  'RefImagePrep', '控制点参考影像预处理',
  '预处理参考影像以获取最佳控制点采集结果。',
  50, 'GXLWorkflow', 'PrmXmlType', 'RefImagePrepParameters',
  '参考影像预处理主作业参数');
  
SELECT install_job (
  '${GXL_ROOT}/PGS/GCPCollection/JPS/py/RefImagePrepChildJob.pyc', 17,
  'RefImagePrepChild', '控制点参考影像预处理子作业',
  '预处理参考影像以获取最佳控制点采集结果。',
  50, 'GXLWorkflow', 'PrmXmlType', 'RefImagePrepChildParameters',
  '参考影像预处理子作业参数');
