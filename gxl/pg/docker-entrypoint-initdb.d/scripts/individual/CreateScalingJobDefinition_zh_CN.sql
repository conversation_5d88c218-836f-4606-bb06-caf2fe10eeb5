-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/Scaling/JPS/py/MasterScaling.pyc', 1.0,
  'GXLMasterScaling', '位深调整',
  '影像位深调整或量化。', 50, 'GXLWorkflow',
  'PrmXmlType', 'MasterScalingParameters', '位深调整主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Scaling/JPS/py/Scaling.pyc', 21, 'GXLScaling', '位深调整子作业',
  '影像位深调整或量化。', 50, 'GXLWorkflow', 'PrmXmlType',
  'ScalingParameters', '位深调整子作业参数');
