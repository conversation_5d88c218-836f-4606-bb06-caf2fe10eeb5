﻿-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/Mosaicking/MosaicPreparation/JPS/py/MosaicUpdatePreparation.pyc',
  34, 'GXLMosaicUpdatePreparation', '镶嵌更新预处理',
  '为以新图像更新已有的镶嵌图作准备。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MosaicUpdatePreparationParameters', 
  '镶嵌更新预处理作业参数');
