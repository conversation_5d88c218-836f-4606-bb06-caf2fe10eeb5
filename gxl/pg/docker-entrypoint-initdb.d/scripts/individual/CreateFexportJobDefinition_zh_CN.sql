-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/Fexport/JPS/py/MasterFexport.pyc', 1.0,
  'MasterFexport', '文件输出',
  '将地理空间数据从一种文件格式转换到另一种。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MasterFexportParameters', null );
SELECT install_job (
  '${GXL_ROOT}/PGS/Fexport/JPS/py/Fexport.pyc', 25.0,
  'Fexport', '文件输出子作业',
  '将地理空间数据从一种文件格式转换到另一种。',
  50, 'GXLWorkflow', 'PrmXmlType', 'FexportParameters', null );
