﻿-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/ComboDEMExtraction2Job.pyc', 1,
  'ComboDEMExtractionSatJob', '多类像对组合DEM提取',
  '对不同的像对组合，生成不同的地理编码DSM与DTM产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ComboDEMExtraction2JobParameters', null);
