-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/DEMIndexCreator/JPS/py/DEMIndexCreator.pyc', 15,
  'IndexPixCreator', '创建PIX索引文件',
  '为多个影像文件创建PIX格式索引文件（index.pix）。',
  50, 'GXLWorkflow', 'PrmXmlType', 'IndexPixCreatorParameters',
  '创建PIX索引文件作业参数');
