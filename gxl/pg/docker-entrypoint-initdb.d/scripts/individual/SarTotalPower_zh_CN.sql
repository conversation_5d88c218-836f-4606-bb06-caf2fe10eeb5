-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/sar/JPS/py/TotalPowerMasterJob.pyc',
  1.0, 'TotalPowerMaster', '雷达总功率',
  '生成雷达影像的总功率。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'TotalPowerParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/sar/JPS/py/TotalPowerJob.pyc',
  21.0, 'TotalPower', '雷达总功率子作业',
  '生成单一雷达影像的总功率。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'TotalPowerParameters', null);
