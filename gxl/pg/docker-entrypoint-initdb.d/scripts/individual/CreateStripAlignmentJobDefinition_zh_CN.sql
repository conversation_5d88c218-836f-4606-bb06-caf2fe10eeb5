-- Copyright (c) 2016. All rights reserved  -  PCI Geomatics
-- 90 Allstate Parkway, Markham, Ontario, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/StripAlignmentJob.pyc',
  1, 'StripAlignment', '同轨影像调整',
  '对同轨影像进行调整',
  50, 'GXLWorkflow', 'PrmXmlType', 'StripAlignmentParameters', null);

