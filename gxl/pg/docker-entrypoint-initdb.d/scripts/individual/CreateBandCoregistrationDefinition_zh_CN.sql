-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/BandCoregistration/JPS/py/MasterBandCoregistration.pyc', 1,
  'MasterBandCoregistration', '波段配准',
  '搜索有效多光谱影像，创建波段配准子作业。',
  50, 'GXLWorkflow', 'PrmXmlType', 'BandCoregistrationParameters',
  '波段配准主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/BandCoregistration/JPS/py/BandCoregistration.pyc', 20,
  'BandCoregistration', '波段配准子作业',
  '对一景多光谱影像的多波段配准，以主波段为基准。',
  50, 'GXLWorkflow', 'PrmXmlType', 'BandCoregistrationParameters',
  '波段配准子作业参数');
