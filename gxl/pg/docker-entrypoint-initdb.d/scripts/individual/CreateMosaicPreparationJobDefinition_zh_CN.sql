-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/Mosaicking/MosaicPreparation/JPS/py/MosaicPreparation.pyc', 
  34, 'GXLMosaicPreparation', '镶嵌预处理',
  '执行产生高质量影像镶嵌所需要的所有必需的预镶嵌处理。包括色彩均衡、接边线生成、影像标准化和影像次序指定。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MosaicPreparationParameters',
  '镶嵌预处理主作业参数');
