-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/ChangeDetection/JPS/py/SARChangeDetectionJob.pyc',
  20.0, 'SARChangeDetection', 'SAR 变化检测',
  '生成两个极化参数文件之间的差异。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'SARChangeDetectionParameters', null);
