﻿-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/MergeDEMsJob.pyc', 1,
  'MergeDEMsJob', '合成多幅DSM/DTMs',
  '对三线阵立体像对组合结果进行合成。输入DSM/DTMs可以是分幅结果。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MergeDEMsJobParameters', null);

SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/MergeDEMsChildJob.pyc', 15,
  'MergeDEMsChildJob', '合成一幅DSM/DTMs',
  '对一幅三线阵立体像对组合结果进行合成。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MergeDEMsChildJobParameters', null);
  