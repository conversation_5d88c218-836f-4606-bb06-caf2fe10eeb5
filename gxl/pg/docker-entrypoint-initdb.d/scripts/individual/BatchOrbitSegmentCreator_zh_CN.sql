﻿-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/OrbitSegmentCreator/JPS/py/BatchOrbitSegmentCreator.pyc',
  1.0, 'BatchOrbitSegmentCreator', '创建轨道段',
  '生成多景影像的轨道段。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'BatchOrbitSegmentCreatorParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/OrbitSegmentCreator/JPS/py/OrbitSegmentCreator.pyc',
  15.0, 'OrbitSegmentCreator', '创建轨道段子作业',
  '生成轨道段。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'OrbitSegmentCreatorParameters', null);

