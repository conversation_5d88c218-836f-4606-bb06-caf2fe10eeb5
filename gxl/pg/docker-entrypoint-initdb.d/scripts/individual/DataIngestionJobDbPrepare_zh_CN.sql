﻿-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/DataIngestion/JPS/py/MasterDataIngest.pyc', 1.0,
  'GXLMasterDataIngest', '影像导入',
  '发现并导入支持的卫星影像。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'DataIngestParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/DataIngestion/JPS/py/DataIngest.pyc', 40.0, 'GXLDataIngest',
  '影像导入子作业', '导入支持的卫星影像。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'DataIngestParameters', null);
