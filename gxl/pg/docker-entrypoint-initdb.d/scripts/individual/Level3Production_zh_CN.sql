﻿-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/workflows/JPS/py/Level3Production.pyc', 1,
  'Level3Production', '三级数据生产',
  '从原始影像自动生成三级影像产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'Level3ProductionParameters', null);
