-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/Tile/JPS/py/MasterTile.pyc',
  1, 'GXLMasterTile', '图像切块',
  '生成多个分块图像。',
  50, 'GXLWorkflow', 'PrmXmlType', 'TileParameters',
  '图像切块主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Tile/JPS/py/Tile.pyc', 25,
  'GXLTile', '图像切块子作业',
  '生成多个分块图像。',
  50, 'GXLWorkflow', 'PrmXmlType', 'TileParameters',
  '图像切块子作业参数');
