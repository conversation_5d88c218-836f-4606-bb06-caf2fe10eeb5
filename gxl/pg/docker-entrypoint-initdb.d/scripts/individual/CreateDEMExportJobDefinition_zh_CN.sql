-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExport/JPS/py/DEMExportJob.pyc', 1,
  'DEMExport', 'DEM产品输出',
  '输出DEM、图像及RPC。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DEMExportParameters',
  'DEM产品输出作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExport/JPS/py/ExportDEMsJob.pyc', 1,
  'ExportDEMs', '输出DEM',
  '输出DEM到LAS或TIF文件。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ExportDEMsParameters',
  '输出DEM作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExport/JPS/py/ExportDEMChildJob.pyc', 28,
  'ExportDEMChild', '输出DEM子作业',
  '输出DEM到LAS或TIF文件。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ExportDEMChildParameters',
  '输出DEM子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExport/JPS/py/ImagesRPCExportJob.pyc', 1,
  'ImagesRPCExport', '图像RPC输出',
  '输出RPC到文本文件同时输出图像到TIF文件。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImagesRPCExportParameters',
  '图像RPC输出作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExport/JPS/py/ImageRPCExportChildJob.pyc', 28,
  'ImageRPCExportChild', '图像RPC输出子作业',
  '输出RPC到文本文件同时输出图像到TIF文件。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageRPCExportChildParameters',
  '图像RPC输出子作业参数');
