-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/Pansharpen/JPS/py/MasterPansharp.pyc', 1,
  'GXLMasterPansharp', '锐化融合',
  '融合全色与多光谱影像对，生成影像融合产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'PansharpParameters',
  '锐化融合主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Pansharpen/JPS/py/Pansharp.pyc', 21, 'GXLPansharp',
  '锐化融合子作业',
  '对指定的全色与多光谱影像对融合，生成融合影像产品。', 50,
  'GXLWorkflow', 'PrmXmlType','PansharpParameters','锐化融合子作业参数');
