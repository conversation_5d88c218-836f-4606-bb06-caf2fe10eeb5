﻿-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/AtmosphericCorrection/CloudMaskHazeRemoval/JPS/py/MasterCloudMaskHazeRemoval.pyc',
  1, 'AtCorMasterCloudMaskHazeRemoval', '云检测与雾去除',
  '生成云掩膜并从多个影像中去除雾霾。',
  50, 'GXLWorkflow', 'PrmXmlType', 'AtCorMasterCloudMaskHazeRemovalParameters',
  '云检测与雾去除主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/AtmosphericCorrection/CloudMaskHazeRemoval/JPS/py/CloudMaskHazeRemoval.pyc',
  40, 'AtCorCloudMaskHazeRemoval', '云检测与雾去除子作业',
  '生成云掩膜并从一个影像中去除雾霾。',
  50, 'GXLWorkflow', 'PrmXmlType', 'AtCorCloudMaskHazeRemovalParameters',
  '云检测与雾去除子作业参数');
