﻿-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/Pyramid/JPS/py/PyramidJob.pyc',
  1.0, 'PyramidJob', '金字塔',
  '产生预览图像金字塔。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'PyramidJobParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/Pyramid/JPS/py/PyramidChildJob.pyc',
  21.0, 'PyramidChildJob', '金字塔子作业',
  '产生预览图像金字塔。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'PyramidChildJobParameters', null);
