-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/AutomaticAccuracyAssessment/JPS/py/AutomaticAccuracyAssessmentJob.pyc',
  10, 'AutomaticAccuracyAssessment', '自动精度评估',
  '在单景图像、控制图像组或相邻的正射图像间提取检查点。',
  50, 'GXLWorkflow', 'PrmXmlType', 'AutomaticAccuracyAssessmentParameters',
  '自动精度评估主作业参数');

SELECT install_job (
  '${GXL_ROOT}/PGS/AutomaticAccuracyAssessment/JPS/py/AutomaticAccuracyAssessmentChildJob.pyc',
  30, 'AutomaticAccuracyAssessmentChild', '自动精度评估子作业',
  '在单景图像、控制图像组或相邻的正射图像间提取检查点。',
  50, 'GXLWorkflow', 'PrmXmlType', 'AutomaticAccuracyAssessmentChildParameters',
  '自动精度评估子作业参数');
