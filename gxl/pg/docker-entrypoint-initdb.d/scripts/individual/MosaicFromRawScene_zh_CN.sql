﻿-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/workflows/JPS/py/MosaicFromRawScene.pyc', 1,
  'MosaicFromRawScene', '从原始影像到镶嵌',
  '创建一个从原始数据供应商卫星影像到镶嵌产品的工作流生产链。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MosaicFromRawSceneParameters', null);
