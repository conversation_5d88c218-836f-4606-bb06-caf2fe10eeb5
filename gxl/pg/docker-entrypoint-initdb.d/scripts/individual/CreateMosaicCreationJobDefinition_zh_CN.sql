-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/Mosaicking/MosaicGeneration/JPS/py/MasterMosaicGeneration.pyc',
  1, 'GXLMasterMosaicGeneration', '镶嵌',
  '实际创建最终输出镶嵌文件。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MasterMosaicGenerationParameters',
  '镶嵌主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Mosaicking/MosaicGeneration/JPS/py/MosaicGeneration.pyc', 21,
  'GXLMosaicGeneration', '镶嵌子作业', '创建镶嵌影像块。', 50,
  'GXLWorkflow', 'PrmXmlType', 'MosaicGenerationParameters',
  '镶嵌子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Mosaicking/MosaicGeneration/JPS/py/MasterClipROI.pyc', 1,
  'GXLMasterClipROI', '镶嵌影像裁切感兴趣区域',
  '从已存在的镶嵌影像中裁切感兴趣区域（AOI）。', 50, 'GXLWorkflow',
  'PrmXmlType', 'MasterClipROIParameters', '镶嵌影像裁切感兴趣区域主作业参数');