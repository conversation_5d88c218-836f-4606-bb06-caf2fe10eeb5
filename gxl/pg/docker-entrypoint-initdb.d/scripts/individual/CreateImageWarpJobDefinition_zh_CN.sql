-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/ImageWarp/JPS/py/MasterImageWarp.pyc',
  1, 'MasterImageWarp', '影像拟合', '影像拟合。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageWarpParameters',
  '影像拟合主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/ImageWarp/JPS/py/ImageWarp.pyc', 21,
  'ImageWarp', '影像拟合子作业',
  '对指定影像拟合。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageWarpParameters',
  '影像拟合子作业参数');
