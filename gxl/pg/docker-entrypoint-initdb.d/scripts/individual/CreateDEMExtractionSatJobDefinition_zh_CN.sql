-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/DEMExtraction2Job.pyc', 1,
  'DEMExtractionSatJob', 'DEM提取',
  '对一至多立体像对提取DEM，并生成地理编码的光栅DEM。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DEMExtraction2JobParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/EpipolarDEMJob.pyc', 1,
  'EpipolarDEMJob', '核线影像产生、提取核线',
  '创建立体像对的核线影像、提取核线DEM。',
  50, 'GXLWorkflow', 'PrmXmlType', 'EpipolarDEMJobParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/EpipolarDEMChildJob.pyc', 17,
  'EpipolarDEMChildJob', '核线影像产生、提取核线子作业',
  '创建立一组体像对的核线影像、提取核线DEM。',
  50, 'GXLWorkflow', 'PrmXmlType', 'EpipolarDEMChildJobParameters', null);
