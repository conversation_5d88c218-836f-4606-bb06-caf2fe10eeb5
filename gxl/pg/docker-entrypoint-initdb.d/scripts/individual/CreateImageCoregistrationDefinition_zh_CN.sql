-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/BandCoregistration/JPS/py/MasterImageCoregistration.pyc', 1,
  'MasterImageCoregistration', '影像配准',
  '影像与影像配准。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageCoregistrationParameters',
  '影像配准主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/BandCoregistration/JPS/py/ImageCoregistration.pyc', 35,
  'ImageCoregistration', '影像配准子作业',
  '一景影像与参考影像配准。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageCoregistrationParameters',
  '影像配准子作业参数');
