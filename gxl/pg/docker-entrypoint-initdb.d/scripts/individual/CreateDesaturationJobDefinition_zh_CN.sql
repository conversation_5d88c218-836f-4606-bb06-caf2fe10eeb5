-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/Filtering/JPS/py/DesaturationJob.pyc', 1.0,
  'ImageDesaturation', '影像去饱和度',
  '对多景图像应用去饱和度滤波。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageDesaturationParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/Filtering/JPS/py/DesaturationChildJob.pyc', 21,
  'ImageDesaturationChild', '影像去饱和度子作业',
  '对单景图像应用去饱和度滤波。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageDesaturationChildParameters', null);
