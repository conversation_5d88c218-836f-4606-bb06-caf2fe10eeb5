-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/DEMIndexCreator/JPS/py/DEMIndexCreator.pyc', 15,
  'DEMIndexCreator', '创建DEM索引文件',
  '为分块的DEM文件创建文本和PIX格式索引文件。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DEMIndexCreatorParameters',
  '创建DEM索引文件作业参数');
