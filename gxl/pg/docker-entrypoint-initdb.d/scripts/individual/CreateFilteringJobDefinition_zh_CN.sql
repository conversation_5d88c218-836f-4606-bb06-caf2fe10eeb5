-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/Filtering/JPS/py/MasterFiltering.pyc',
  1.0, 'GXLMasterFiltering', '自适应滤波',
  '自适应图像滤波。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MasterFilteringParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/Filtering/JPS/py/Filtering.pyc', 21,
  'GXLFiltering', '自适应滤波子作业',
  '自适应图像滤波。',
  50, 'GXLWorkflow', 'PrmXmlType', 'FilteringParameters', null);
