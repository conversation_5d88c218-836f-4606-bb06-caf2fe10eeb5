-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/VDem/JPS/py/VDemPreparerJob.pyc', 34.0, 'VDemPreparer',
  '高程数据准备', '准备生成瓦块DEM所用到的矢量文件。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'VDemPreparerParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/VDem/JPS/py/VDemGeneratorJob.pyc', 34.0, 'VDemGenerator',
  'DEM生成子作业',
  '从矢量文件产生单个光栅DEM块。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'VDemGeneratorParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/VDem/JPS/py/VDemGeneratorMasterJob.pyc',
  1.0, 'VDemGeneratorMaster', 'DEM生成',
  '从矢量文件产生多个光栅DEM块。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'VDemGeneratorParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/VDem/JPS/py/VDemMasterJob.pyc', 1.0, 'VDemMaster',
  'DEM生成', '从矢量数据生成分块的光栅DEM。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'VDemMasterParameters', null);
