-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/ColorBalancing/JPS/py/MasterColorBalance.pyc', 1.0,
  'GXLMasterColorBalance', '色彩均衡应用',
  '把镶嵌预处理作业生成的色彩均衡系数应用到单景影像中，生成色彩均衡后的影像。',
  50, 'GXLWorkflow','PrmXmlType', 'MasterColorBalanceParameters', null );
SELECT install_job (
  '${GXL_ROOT}/PGS/ColorBalancing/JPS/py/ColorBalance.pyc', 40.0,
  'GXLColorBalance', '色彩均衡应用子作业',
  '把镶嵌预处理作业生成的色彩均衡系数应用到单景影像中，生成色彩均衡后的影像。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ColorBalanceParameters', null );
