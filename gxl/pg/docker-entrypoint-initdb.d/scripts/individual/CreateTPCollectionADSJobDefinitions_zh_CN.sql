-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/TPCollectionRefinementADSJob.pyc',
  1, 'TPCollectionRefinementADS', 'ADS 同名点采集与优化',
  '对一系列ADS影像条带进行同名点采集与优化。',
  50, 'GXLWorkflow', 'PrmXmlType', 'TPParameters',
  'TP作业参数');

  SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/TPRefinementADS.pyc',
  19, 'TPRefinementADS', 'ADS 同名点优化',
  '在基于ADS的OrthoEngine工程上执行同名点优化。',
  50, 'GXLWorkflow', 'PrmXmlType', 'TPParameters',
  'TP作业参数');

SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/TPCollectionADS.pyc',
  1, 'TPCollectionADS', 'ADS 同名点采集',
  '在一系列ADS影像条带间进行同名点采集。',
  50, 'GXLWorkflow', 'PrmXmlType', 'TPParameters',
  'TP作业参数');

SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/TPCollectionADSChild.pyc',
  19, 'TPCollectionADSChild', 'ADS 同名点采集子作业',
  '在基于ADS的OrthoEngine工程的子集上进行同名点采集。',
  50, 'GXLWorkflow', 'PrmXmlType', 'TPParameters',
  'TP作业参数');


