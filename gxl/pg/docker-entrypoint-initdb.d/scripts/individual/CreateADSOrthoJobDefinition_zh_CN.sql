-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
  '${GXL_ROOT}/PGS/Orthorectification/JPS/py/ADSOrtho.pyc', 1,
  'ADSOrtho', 'ADS影像正射校正',
  'ADS影像正射校正。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ADSOrthoParameters',
  'ADS影像正射校正作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Orthorectification/JPS/py/ADSOrthoChild.pyc', 21,
  'ADSOrthoChild', 'ADS影像正射校正子作业',
  '单景ADS影像正射校正。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ADSOrthoParameters',
  'ADS影像正射校正子作业参数');
