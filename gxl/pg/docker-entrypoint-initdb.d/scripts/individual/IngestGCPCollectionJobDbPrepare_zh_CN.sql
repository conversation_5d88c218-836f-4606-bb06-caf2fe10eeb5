-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script is written for PostgreSQL version 8.3.


-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
-- This script fills the JPS database with information so that processing
-- servers can run the GXL jobs.
-- +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

SELECT install_job (
 '${GXL_ROOT}/PGS/DataIngestion/JPS/py/MasterIngestGCPCollection.pyc',
 1.0, 'MasterIngestGCPCollection', '影像导入与控制点采集',
 '发现并导入支持的卫星影像，然后提取控制点。',
 50.0, 'GXLWorkflow', 'PrmXmlType', 'IngestGCPParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/DataIngestion/JPS/py/IngestGCPCollection.pyc',
  26.0, 'IngestGCPCollection', '影像导入与控制点提取子作业',
  '发现并导入支持的卫星影像，然后提取控制点。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'IngestGCPParameters', null);
