﻿
-- Copyright (c) 2009. All rights reserved  -  PCI Geomatics
-- 50 West Wilmot Street, Richmond Hill, Ont, Canada.
-- Not to be used, reproduced or disclosed without permission.

-- This script was written for POSTGRESQL version 8.3 and can be executed 
-- by PostgreSQL pgAdmin tool version 1.10 or higher 

-- Fill the JPS database with common information for all GXL modules.

BEGIN;

  -- Insert default user
  -- WARNING: Change this USER to improve security
  INSERT INTO users( user_name, user_password, first_name,
                     last_name, email, description, is_admin )
  SELECT 'gxladmin', 'gxladmin_password_abc',
           'GXL', 'GXL', '<EMAIL>', 
           'Admin user to run GXL jobs', TRUE WHERE NOT EXISTS
        (SELECT 1 FROM users WHERE user_name='gxladmin');


  -- Insert server configuration 
  INSERT INTO server_configs( 
     name, description, resources_capacity, jobs_per_poll_limit, web_service_url, 
     job_poll_interval_seconds, ping_interval_seconds, default_working_dir )
  SELECT 'GXLWorkflowServerConfig', 'Server Configuration for running GXL jobs',
    100.0, 100, 'http://localhost:8080/jps/job/', 1.0,  60.0, null WHERE NOT
    EXISTS (SELECT 1 FROM server_configs WHERE name='GXLWorkflowServerConfig');


  -- Insert category
  INSERT INTO
    categories( "name", description )
  SELECT 'GXLWorkflow', 'Category for GXL workflows/modules/jobs' WHERE NOT
  EXISTS (SELECT 1 FROM categories WHERE name='GXLWorkflow');

  -- Connect server config to GXL categories
  INSERT INTO server_categories (sc_id, cat_id) 
  SELECT ( SELECT sc_id FROM server_configs  WHERE name = 'GXLWorkflowServerConfig' ), 
    ( SELECT cat_id FROM categories WHERE name = 'GXLWorkflow' ) WHERE NOT EXISTS
    (SELECT 1 FROM server_categories INNER JOIN server_configs ON 
        server_categories.sc_id=server_configs.sc_id
        INNER JOIN categories ON server_categories.cat_id=categories.cat_id
        WHERE server_configs.name='GXLWorkflowServerConfig' 
        AND categories.name='GXLWorkflow');


  -- Insert PRM XML parameter definition
  INSERT INTO param_definitions( name, schema, schema_type, is_schema_remote,
     description)
  SELECT 'PrmXmlType', null, 'XMLSCHEMA', TRUE, 
    'GXL PRM XML parameter definition' WHERE NOT EXISTS
    (SELECT 1 FROM param_definitions WHERE name='PrmXmlType');

COMMIT;

SELECT install_job (
  '${GXL_ROOT}/PGS/AtmosphericCorrection/CloudMaskHazeRemoval/JPS/py/MasterCloudMaskHazeRemoval.pyc',
  1, 'AtCorMasterCloudMaskHazeRemoval', '云检测与雾去除',
  '生成云掩膜并从多个影像中去除雾霾。',
  50, 'GXLWorkflow', 'PrmXmlType', 'AtCorMasterCloudMaskHazeRemovalParameters',
  '云检测与雾去除主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/AtmosphericCorrection/CloudMaskHazeRemoval/JPS/py/CloudMaskHazeRemoval.pyc',
  40, 'AtCorCloudMaskHazeRemoval', '云检测与雾去除子作业',
  '生成云掩膜并从一个影像中去除雾霾。',
  50, 'GXLWorkflow', 'PrmXmlType', 'AtCorCloudMaskHazeRemovalParameters',
  '云检测与雾去除子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/OrbitSegmentCreator/JPS/py/BatchOrbitSegmentCreator.pyc',
  1.0, 'BatchOrbitSegmentCreator', '创建轨道段',
  '生成多景影像的轨道段。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'BatchOrbitSegmentCreatorParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/OrbitSegmentCreator/JPS/py/OrbitSegmentCreator.pyc',
  15.0, 'OrbitSegmentCreator', '创建轨道段子作业',
  '生成轨道段。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'OrbitSegmentCreatorParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/ChangeDetection/JPS/py/SARChangeDetectionJob.pyc',
  20.0, 'SARChangeDetection', 'SAR 变化检测',
  '生成两个极化参数文件之间的差异。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'SARChangeDetectionParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/ColorBalancing/JPS/py/MasterColorBalance.pyc', 1.0,
  'GXLMasterColorBalance', '色彩均衡应用',
  '把镶嵌预处理作业生成的色彩均衡系数应用到单景影像中，生成色彩均衡后的影像。',
  50, 'GXLWorkflow','PrmXmlType', 'MasterColorBalanceParameters', null );
SELECT install_job (
  '${GXL_ROOT}/PGS/ColorBalancing/JPS/py/ColorBalance.pyc', 40.0,
  'GXLColorBalance', '色彩均衡应用子作业',
  '把镶嵌预处理作业生成的色彩均衡系数应用到单景影像中，生成色彩均衡后的影像。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ColorBalanceParameters', null );
SELECT install_job (
  '${GXL_ROOT}/PGS/Orthorectification/JPS/py/ADSOrtho.pyc', 1,
  'ADSOrtho', 'ADS影像正射校正',
  'ADS影像正射校正。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ADSOrthoParameters',
  'ADS影像正射校正作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Orthorectification/JPS/py/ADSOrthoChild.pyc', 21,
  'ADSOrthoChild', 'ADS影像正射校正子作业',
  '单景ADS影像正射校正。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ADSOrthoParameters',
  'ADS影像正射校正子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/AdjustDEMJob.pyc', 1,
  'AdjustDEMJob', '修正DEM',
  '使用三维控制点或加密点修正DEM。',
  50, 'GXLWorkflow', 'PrmXmlType', 'AdjustDEMJobParameters', null);
 
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/AdjustDEMChildJob.pyc', 15,
  'AdjustDEMChildJob', '修正DEM子作业',
  '使用三维控制点或加密点修正DEM。',
  50, 'GXLWorkflow', 'PrmXmlType', 'AdjustDEMChildJobParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/AutomaticAccuracyAssessment/JPS/py/AutomaticAccuracyAssessmentJob.pyc',
  10, 'AutomaticAccuracyAssessment', '自动精度评估',
  '在单景图像、控制图像组或相邻的正射图像间提取检查点。',
  50, 'GXLWorkflow', 'PrmXmlType', 'AutomaticAccuracyAssessmentParameters',
  '自动精度评估主作业参数');

SELECT install_job (
  '${GXL_ROOT}/PGS/AutomaticAccuracyAssessment/JPS/py/AutomaticAccuracyAssessmentChildJob.pyc',
  30, 'AutomaticAccuracyAssessmentChild', '自动精度评估子作业',
  '在单景图像、控制图像组或相邻的正射图像间提取检查点。',
  50, 'GXLWorkflow', 'PrmXmlType', 'AutomaticAccuracyAssessmentChildParameters',
  '自动精度评估子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/BandCoregistration/JPS/py/MasterBandCoregistration.pyc', 1,
  'MasterBandCoregistration', '波段配准',
  '搜索有效多光谱影像，创建波段配准子作业。',
  50, 'GXLWorkflow', 'PrmXmlType', 'BandCoregistrationParameters',
  '波段配准主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/BandCoregistration/JPS/py/BandCoregistration.pyc', 20,
  'BandCoregistration', '波段配准子作业',
  '对一景多光谱影像的多波段配准，以主波段为基准。',
  50, 'GXLWorkflow', 'PrmXmlType', 'BandCoregistrationParameters',
  '波段配准子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/BlueBandSimulation/JPS/py/MasterBlueBandSimulation.pyc',
  1, 'GXLMasterBlueBandSimulation', '蓝波段模拟',
  '生成多个模拟的自然彩色图像。',
  50, 'GXLWorkflow', 'PrmXmlType', 'BlueBandSimulationParameters',
  '蓝波段模拟主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/BlueBandSimulation/JPS/py/BlueBandSimulation.pyc', 15,
  'GXLBlueBandSimulation', '蓝波段模拟子作业',
  '生成模拟自然彩色图像。',
  50, 'GXLWorkflow', 'PrmXmlType', 'BlueBandSimulationParameters',
  '蓝波段模拟子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/ComboDEMExtraction2Job.pyc', 1,
  'ComboDEMExtractionSatJob', '多类像对组合DEM提取',
  '对不同的像对组合，生成不同的地理编码DSM与DTM产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ComboDEMExtraction2JobParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/ConvertDSMToDTMJob.pyc', 1,
  'ConvertDSMToDTMJob', 'DEM转换(DSM到DTM)',
  '由DSM产生DTM',
  50, 'GXLWorkflow', 'PrmXmlType', 'ConvertDSMToDTMJobParameters', null);

SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/ConvertDSMToDTMChildJob.pyc', 25,
  'ConvertDSMToDTMChildJob', 'DEM转换(DSM到DTM)子作业',
  '由单一DSM产生单一DTM',
  50, 'GXLWorkflow', 'PrmXmlType', 'ConvertDSMToDTMChildJobParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExport/JPS/py/DEMExportJob.pyc', 1,
  'DEMExport', 'DEM产品输出',
  '输出DEM、图像及RPC。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DEMExportParameters',
  'DEM产品输出作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExport/JPS/py/ExportDEMsJob.pyc', 1,
  'ExportDEMs', '输出DEM',
  '输出DEM到LAS或TIF文件。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ExportDEMsParameters',
  '输出DEM作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExport/JPS/py/ExportDEMChildJob.pyc', 28,
  'ExportDEMChild', '输出DEM子作业',
  '输出DEM到LAS或TIF文件。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ExportDEMChildParameters',
  '输出DEM子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExport/JPS/py/ImagesRPCExportJob.pyc', 1,
  'ImagesRPCExport', '图像RPC输出',
  '输出RPC到文本文件同时输出图像到TIF文件。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImagesRPCExportParameters',
  '图像RPC输出作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExport/JPS/py/ImageRPCExportChildJob.pyc', 28,
  'ImageRPCExportChild', '图像RPC输出子作业',
  '输出RPC到文本文件同时输出图像到TIF文件。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageRPCExportChildParameters',
  '图像RPC输出子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/DEMExtraction2Job.pyc', 1,
  'DEMExtractionSatJob', 'DEM提取',
  '对一至多立体像对提取DEM，并生成地理编码的光栅DEM。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DEMExtraction2JobParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/EpipolarDEMJob.pyc', 1,
  'EpipolarDEMJob', '核线影像产生、提取核线',
  '创建立体像对的核线影像、提取核线DEM。',
  50, 'GXLWorkflow', 'PrmXmlType', 'EpipolarDEMJobParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/EpipolarDEMChildJob.pyc', 17,
  'EpipolarDEMChildJob', '核线影像产生、提取核线子作业',
  '创建立一组体像对的核线影像、提取核线DEM。',
  50, 'GXLWorkflow', 'PrmXmlType', 'EpipolarDEMChildJobParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMIndexCreator/JPS/py/DEMIndexCreator.pyc', 15,
  'DEMIndexCreator', '创建DEM索引文件',
  '为分块的DEM文件创建文本和PIX格式索引文件。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DEMIndexCreatorParameters',
  '创建DEM索引文件作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Mosaicking/MosaicGeneration/JPS/py/DEMMosaicGeneration.pyc',
  1, 'DEMMosaicGeneration', 'DEM镶嵌',
  '生成DEM镶嵌的所有瓦块。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DEMMosaicGenerationParameters',
  'DEM镶嵌作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Mosaicking/MosaicGeneration/JPS/py/MosaicGeneration.pyc', 21,
  'DEMMosaicGenerationChild', 'DEM镶嵌子作业',
  '生成DEM镶嵌瓦块。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DEMMosaicGenerationParameters',
  'DEM镶嵌子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/DEMMosaicJob.pyc', 1,
  'DEMMosaicJob', 'DEM提取镶嵌',
  '创建从立体像对核线数据提取的DSM和DTM的镶嵌影像。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DEMMosaicJobParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/DataTransformation/JPS/py/MasterDataTransformation.pyc',
  1, 'MasterDataTransformation', '空间数据转换',
  '搜寻指定目录下的所有矢量或栅格文件，创建数据格式或/和投影变换。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DataTransformationParameters',
  '空间数据转换主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DataTransformation/JPS/py/DataTransformation.pyc', 21,
  'DataTransformation', '空间数据转换子作业',
  '对指定的矢量或光栅文件进行格式或/和投影变换。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DataTransformationParameters',
  '空间数据转换子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Filtering/JPS/py/DesaturationJob.pyc', 1.0,
  'ImageDesaturation', '影像去饱和度',
  '对多景图像应用去饱和度滤波。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageDesaturationParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/Filtering/JPS/py/DesaturationChildJob.pyc', 21,
  'ImageDesaturationChild', '影像去饱和度子作业',
  '对单景图像应用去饱和度滤波。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageDesaturationChildParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/Fexport/JPS/py/MasterFexport.pyc', 1.0,
  'MasterFexport', '文件输出',
  '将地理空间数据从一种文件格式转换到另一种。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MasterFexportParameters', null );
SELECT install_job (
  '${GXL_ROOT}/PGS/Fexport/JPS/py/Fexport.pyc', 25.0,
  'Fexport', '文件输出子作业',
  '将地理空间数据从一种文件格式转换到另一种。',
  50, 'GXLWorkflow', 'PrmXmlType', 'FexportParameters', null );
SELECT install_job (
  '${GXL_ROOT}/PGS/Filtering/JPS/py/MasterFiltering.pyc',
  1.0, 'GXLMasterFiltering', '自适应滤波',
  '自适应图像滤波。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MasterFilteringParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/Filtering/JPS/py/Filtering.pyc', 21,
  'GXLFiltering', '自适应滤波子作业',
  '自适应图像滤波。',
  50, 'GXLWorkflow', 'PrmXmlType', 'FilteringParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/GCPCollection/JPS/py/MasterGCPCollection.pyc', 1,
  'MasterGCPCollection', '控制点采集',
  '采集控制点。',
  50, 'GXLWorkflow', 'PrmXmlType', 'GCPCollectionParameters',
  '控制点采集主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/GCPCollection/JPS/py/GCPCollectionJob.pyc', 15,
  'GCPCollection', '控制点采集子作业',
  '为指定的影像采集控制点。',
  50, 'GXLWorkflow', 'PrmXmlType', 'GCPCollectionParameters',
  '控制点采集子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/ImageEnhancement/JPS/py/MasterImageEnhancement.pyc', 1,
  'GXLMasterImageEnhancement', '影像增强',
  '使用不同的增强算法对指定影像进行永久增强。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageEnhancementParameters',
  '影像增强主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/ImageEnhancement/JPS/py/ImageEnhancement.pyc', 20,
  'GXLImageEnhancement', '影像增强子作业',
  '使用不同的增强算法对指定影像进行永久增强。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageEnhancementParameters',
  '影像增强子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/BandCoregistration/JPS/py/MasterImageCoregistration.pyc', 1,
  'MasterImageCoregistration', '影像配准',
  '影像与影像配准。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageCoregistrationParameters',
  '影像配准主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/BandCoregistration/JPS/py/ImageCoregistration.pyc', 35,
  'ImageCoregistration', '影像配准子作业',
  '一景影像与参考影像配准。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageCoregistrationParameters',
  '影像配准子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/ImageWarp/JPS/py/MasterImageWarp.pyc',
  1, 'MasterImageWarp', '影像拟合', '影像拟合。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageWarpParameters',
  '影像拟合主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/ImageWarp/JPS/py/ImageWarp.pyc', 21,
  'ImageWarp', '影像拟合子作业',
  '对指定影像拟合。',
  50, 'GXLWorkflow', 'PrmXmlType', 'ImageWarpParameters',
  '影像拟合子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMIndexCreator/JPS/py/DEMIndexCreator.pyc', 15,
  'IndexPixCreator', '创建PIX索引文件',
  '为多个影像文件创建PIX格式索引文件（index.pix）。',
  50, 'GXLWorkflow', 'PrmXmlType', 'IndexPixCreatorParameters',
  '创建PIX索引文件作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/MergeDEMsJob.pyc', 1,
  'MergeDEMsJob', '合成多幅DSM/DTMs',
  '对三线阵立体像对组合结果进行合成。输入DSM/DTMs可以是分幅结果。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MergeDEMsJobParameters', null);

SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/MergeDEMsChildJob.pyc', 15,
  'MergeDEMsChildJob', '合成一幅DSM/DTMs',
  '对一幅三线阵立体像对组合结果进行合成。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MergeDEMsChildJobParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/Mosaicking/MosaicGeneration/JPS/py/MasterMosaicGeneration.pyc',
  1, 'GXLMasterMosaicGeneration', '镶嵌',
  '实际创建最终输出镶嵌文件。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MasterMosaicGenerationParameters',
  '镶嵌主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Mosaicking/MosaicGeneration/JPS/py/MosaicGeneration.pyc', 21,
  'GXLMosaicGeneration', '镶嵌子作业', '创建镶嵌影像块。', 50,
  'GXLWorkflow', 'PrmXmlType', 'MosaicGenerationParameters',
  '镶嵌子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Mosaicking/MosaicGeneration/JPS/py/MasterClipROI.pyc', 1,
  'GXLMasterClipROI', '镶嵌影像裁切感兴趣区域',
  '从已存在的镶嵌影像中裁切感兴趣区域（AOI）。', 50, 'GXLWorkflow',
  'PrmXmlType', 'MasterClipROIParameters', '镶嵌影像裁切感兴趣区域主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Mosaicking/MosaicPreparation/JPS/py/MosaicPreparation.pyc', 
  34, 'GXLMosaicPreparation', '镶嵌预处理',
  '执行产生高质量影像镶嵌所需要的所有必需的预镶嵌处理。包括色彩均衡、接边线生成、影像标准化和影像次序指定。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MosaicPreparationParameters',
  '镶嵌预处理主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Mosaicking/MosaicGeneration/JPS/py/MasterMosaicUpdateGeneration.pyc',
  1, 'GXLMasterMosaicUpdateGeneration', '镶嵌更新',
  '依照镶嵌更新预处理模块，以新图像更新已有镶嵌影像。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MasterMosaicUpdateGenerationParameters',
  '镶嵌更新作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Mosaicking/MosaicPreparation/JPS/py/MosaicUpdatePreparation.pyc',
  34, 'GXLMosaicUpdatePreparation', '镶嵌更新预处理',
  '为以新图像更新已有的镶嵌图作准备。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MosaicUpdatePreparationParameters', 
  '镶嵌更新预处理作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Orthorectification/JPS/py/MasterOrtho.pyc', 1,
  'GXLMasterOrtho', '正射校正',
  '影像正射校正，生成正射产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'OrthoParameters',
  '正射校正主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Orthorectification/JPS/py/Ortho.pyc', 21,
  'GXLOrtho', '正射校正子作业',
  '对指定的影像正射校正，生成正射产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'OrthoParameters',
  '正射校正子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Pansharpen/JPS/py/MasterPansharp.pyc', 1,
  'GXLMasterPansharp', '锐化融合',
  '融合全色与多光谱影像对，生成影像融合产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'PansharpParameters',
  '锐化融合主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Pansharpen/JPS/py/Pansharp.pyc', 21, 'GXLPansharp',
  '锐化融合子作业',
  '对指定的全色与多光谱影像对融合，生成融合影像产品。', 50,
  'GXLWorkflow', 'PrmXmlType','PansharpParameters','锐化融合子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/GCPCollection/JPS/py/RefImagePrepJob.pyc', 1,
  'RefImagePrep', '控制点参考影像预处理',
  '预处理参考影像以获取最佳控制点采集结果。',
  50, 'GXLWorkflow', 'PrmXmlType', 'RefImagePrepParameters',
  '参考影像预处理主作业参数');
  
SELECT install_job (
  '${GXL_ROOT}/PGS/GCPCollection/JPS/py/RefImagePrepChildJob.pyc', 17,
  'RefImagePrepChild', '控制点参考影像预处理子作业',
  '预处理参考影像以获取最佳控制点采集结果。',
  50, 'GXLWorkflow', 'PrmXmlType', 'RefImagePrepChildParameters',
  '参考影像预处理子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Scaling/JPS/py/MasterScaling.pyc', 1.0,
  'GXLMasterScaling', '位深调整',
  '影像位深调整或量化。', 50, 'GXLWorkflow',
  'PrmXmlType', 'MasterScalingParameters', '位深调整主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Scaling/JPS/py/Scaling.pyc', 21, 'GXLScaling', '位深调整子作业',
  '影像位深调整或量化。', 50, 'GXLWorkflow', 'PrmXmlType',
  'ScalingParameters', '位深调整子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/StripAlignmentJob.pyc',
  1, 'StripAlignment', '同轨影像调整',
  '对同轨影像进行调整',
  50, 'GXLWorkflow', 'PrmXmlType', 'StripAlignmentParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/TPCollectionRefinementADSJob.pyc',
  1, 'TPCollectionRefinementADS', 'ADS 同名点采集与优化',
  '对一系列ADS影像条带进行同名点采集与优化。',
  50, 'GXLWorkflow', 'PrmXmlType', 'TPParameters',
  'TP作业参数');

  SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/TPRefinementADS.pyc',
  19, 'TPRefinementADS', 'ADS 同名点优化',
  '在基于ADS的OrthoEngine工程上执行同名点优化。',
  50, 'GXLWorkflow', 'PrmXmlType', 'TPParameters',
  'TP作业参数');

SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/TPCollectionADS.pyc',
  1, 'TPCollectionADS', 'ADS 同名点采集',
  '在一系列ADS影像条带间进行同名点采集。',
  50, 'GXLWorkflow', 'PrmXmlType', 'TPParameters',
  'TP作业参数');

SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/TPCollectionADSChild.pyc',
  19, 'TPCollectionADSChild', 'ADS 同名点采集子作业',
  '在基于ADS的OrthoEngine工程的子集上进行同名点采集。',
  50, 'GXLWorkflow', 'PrmXmlType', 'TPParameters',
  'TP作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/MasterBundleAdjustment.pyc',
  1, 'GXLMasterBundleAdjustment', '同名点采集与优化',
  '对一系列影像进行同名点采集与优化。',
  50, 'GXLWorkflow', 'PrmXmlType', 'BundleAdjustmentParameters',
  'TP作业参数');

SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/TPCollection.pyc',
  26, 'TPCollection', '同名点采集',
  '对一系列影像进行同名点采集。',
  50, 'GXLWorkflow', 'PrmXmlType', 'TPCollectionParameters',
  'TP采集子作业参数');

SELECT install_job (
  '${GXL_ROOT}/PGS/BundleAdjustment/JPS/py/BundleAdjustment.pyc',
  19, 'GXLBundleAdjustment', '同名点优化',
  '对所有相关影像进行同名点优化。',
  50, 'GXLWorkflow', 'PrmXmlType', 'BundleAdjustmentParameters',
  'TP作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Tile/JPS/py/MasterTile.pyc',
  1, 'GXLMasterTile', '图像切块',
  '生成多个分块图像。',
  50, 'GXLWorkflow', 'PrmXmlType', 'TileParameters',
  '图像切块主作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Tile/JPS/py/Tile.pyc', 25,
  'GXLTile', '图像切块子作业',
  '生成多个分块图像。',
  50, 'GXLWorkflow', 'PrmXmlType', 'TileParameters',
  '图像切块子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/WaterColourization/JPS/py/MasterWaterColourization.pyc', 1.0,
  'GXLMasterWaterColourization', '水体着色',
  '为影像水体着色。',
  50, 'GXLWorkflow', 'PrmXmlType', 'WaterColourizationParameters',
  '水体着色作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/WaterColourization/JPS/py/WaterColourization.pyc', 15.0,
  'GXLWaterColourization', '水体着色子作业',
  '为单一影像水体着色。',
  50, 'GXLWorkflow', 'PrmXmlType', 'WaterColourizationParameters',
  '水体着色子作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/workflows/JPS/py/DEMProduction.pyc', 1,
  'DEMProduction', '从原始影像生产DEM',
  '从供应商提供的原始卫星影像生成地理编码的DEM和DTM产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'DEMProductionParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/DataIngestion/JPS/py/MasterDataIngest.pyc', 1.0,
  'GXLMasterDataIngest', '影像导入',
  '发现并导入支持的卫星影像。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'DataIngestParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/DataIngestion/JPS/py/DataIngest.pyc', 40.0, 'GXLDataIngest',
  '影像导入子作业', '导入支持的卫星影像。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'DataIngestParameters', null);
SELECT install_job (
 '${GXL_ROOT}/PGS/DataIngestion/JPS/py/MasterIngestGCPCollection.pyc',
 1.0, 'MasterIngestGCPCollection', '影像导入与控制点采集',
 '发现并导入支持的卫星影像，然后提取控制点。',
 50.0, 'GXLWorkflow', 'PrmXmlType', 'IngestGCPParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/DataIngestion/JPS/py/IngestGCPCollection.pyc',
  26.0, 'IngestGCPCollection', '影像导入与控制点提取子作业',
  '发现并导入支持的卫星影像，然后提取控制点。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'IngestGCPParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/workflows/JPS/py/Level3Production.pyc', 1,
  'Level3Production', '三级数据生产',
  '从原始影像自动生成三级影像产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'Level3ProductionParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/workflows/JPS/py/Level4Production.pyc', 1,
  'Level4Production', '四级数据生产',
  '从原始影像自动生成四级影像产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'Level4ProductionParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/workflows/JPS/py/Level5Production.pyc', 1,
  'Level5Production', '正射到镶嵌',
  '从正射卫星影像生成镶嵌影像产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'Level5ProductionParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/workflows/JPS/py/MosaicFromRawScene.pyc', 1,
  'MosaicFromRawScene', '从原始影像到镶嵌',
  '创建一个从原始数据供应商卫星影像到镶嵌产品的工作流生产链。',
  50, 'GXLWorkflow', 'PrmXmlType', 'MosaicFromRawSceneParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/workflows/JPS/py/PansharpenProduction.pyc', 1,
  'PansharpenProduction', '影像锐化融合生产',
  '从原始影像创建锐化融合影像产品。',
  50, 'GXLWorkflow', 'PrmXmlType', 'PansharpenProductionParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/PolarimetricDecomp/JPS/py/PolarimetricDecompGenerationJob.pyc',
  1.0, 'PolarimetricDecompGeneration', '极化参数',
  '从雷达影像中生成极化参数。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'PolarimetricDecompGenerationParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/PolarimetricDecomp/JPS/py/MasterPolarimetricDecompJob.pyc',
  1.0, 'MasterPolarimetricDecomp', '极化参数主作业',
  '从原始雷达影像中生成极化参数。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'PolarimetricParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/PolarimetricDecomp/JPS/py/PolarimetricDecompJob.pyc',
  40.0, 'PolarimetricDecomp', '极化参数子作业',
  '从原始雷达影像中生成极化参数。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'PolarimetricParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/Pyramid/JPS/py/PyramidJob.pyc',
  1.0, 'PyramidJob', '金字塔',
  '产生预览图像金字塔。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'PyramidJobParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/Pyramid/JPS/py/PyramidChildJob.pyc',
  21.0, 'PyramidChildJob', '金字塔子作业',
  '产生预览图像金字塔。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'PyramidChildJobParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/RasterToPolygon/JPS/py/MasterGenerationJob.pyc',
  1.0, 'MasterRasterToPolygonGeneration', '光栅到多边形',
  '从影像中提取多边形。',
  50.0, 'GXLWorkflow','PrmXmlType','RasterToPolygonGenerationParameters',null);
SELECT install_job (
  '${GXL_ROOT}/PGS/RasterToPolygon/JPS/py/GenerationJob.pyc',
  40.0, 'RasterToPolygonGeneration', '光栅到多边形子作业',
  '从单一图像中提取多边形。',
  50.0, 'GXLWorkflow','PrmXmlType', 'RasterToPolygonGenerationParameters',null);
SELECT install_job (
  '${GXL_ROOT}/PGS/DEMExtraction2/JPS/py/SarDEMExtractionJob.pyc', 1,
  'SarDEMExtraction', '雷达DEM提取',
  '从重叠的雷达影像生成地理编码后的DSM和DTM。',
  50, 'GXLWorkflow', 'PrmXmlType', 'SarDEMExtractionParameters',
  '雷达DEM提取作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/DataIngestion/JPS/py/MasterSarDataIngest.pyc',
  1.0, 'MasterSarDataIngest', '雷达影像导入',
  '发现并导入支持的原始雷达影像。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'DataIngestParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/DataIngestion/JPS/py/SarDataIngest.pyc',
  40.0, 'SarDataIngest', '雷达影像导入子作业',
  '导入支持的原始雷达影像。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'DataIngestParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/Orthorectification/JPS/py/SarMasterOrtho.pyc', 1,
  'MasterSarOrtho', '雷达正射校正',
  '对包含有效数学模型的雷达影像正射校正进行。',
  50, 'GXLWorkflow', 'PrmXmlType', 'OrthoParameters',
  '正射校正作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/Orthorectification/JPS/py/SarOrtho.pyc', 21,
  'SarOrtho', '雷达正射校正子作业',
  '对包含有效数学模型的雷达影像正射校正进行。',
  50, 'GXLWorkflow', 'PrmXmlType', 'OrthoParameters',
  '正射校正作业参数');
SELECT install_job (
  '${GXL_ROOT}/PGS/sar/JPS/py/TotalPowerMasterJob.pyc',
  1.0, 'TotalPowerMaster', '雷达总功率',
  '生成雷达影像的总功率。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'TotalPowerParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/sar/JPS/py/TotalPowerJob.pyc',
  21.0, 'TotalPower', '雷达总功率子作业',
  '生成单一雷达影像的总功率。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'TotalPowerParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/VDem/JPS/py/VDemPreparerJob.pyc', 34.0, 'VDemPreparer',
  '高程数据准备', '准备生成瓦块DEM所用到的矢量文件。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'VDemPreparerParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/VDem/JPS/py/VDemGeneratorJob.pyc', 34.0, 'VDemGenerator',
  'DEM生成子作业',
  '从矢量文件产生单个光栅DEM块。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'VDemGeneratorParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/VDem/JPS/py/VDemGeneratorMasterJob.pyc',
  1.0, 'VDemGeneratorMaster', 'DEM生成',
  '从矢量文件产生多个光栅DEM块。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'VDemGeneratorParameters', null);
SELECT install_job (
  '${GXL_ROOT}/PGS/VDem/JPS/py/VDemMasterJob.pyc', 1.0, 'VDemMaster',
  'DEM生成', '从矢量数据生成分块的光栅DEM。',
  50.0, 'GXLWorkflow', 'PrmXmlType', 'VDemMasterParameters', null);
